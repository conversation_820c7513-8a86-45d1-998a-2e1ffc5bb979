{
    'name': 'Premium Car Insurance Request',

    'version': '1.6.66',  # Added company-based access control for multi-company security



    'summary': 'Form for Premium Car Cover – First Inquiry by Sub-Brokers',
    'author': 'Artus',
    'category': 'Insurance',
    'depends': [
        'base',
        'crm',
        'crm_lead_customization',
        'website',
        'contacts',
        'mail',
        'bus'
    ],
    'data': [
        'security/ir.model.access.csv',
        'security/ir_rules.xml',
        'data/ir_sequence_data.xml',
        'data/vehicle_manufacturer_data.xml',
        'views/website_templates.xml',
        'views/premium_request_views.xml',
        'views/error_templates.xml',  # Combined error templates
        'views/augencheck_views.xml',
        'views/augencheck_templates.xml',
        'views/premiumcar_endkunden_views.xml',
        'views/premiumcar_endkunden_templates.xml',
        'views/premiumcar_untervermittler_views.xml',
        'views/premiumcar_untervermittler_templates.xml',
        'views/premiumcar_erstanfrage_endkunden_intern_views.xml',
        'views/premiumcar_erstanfrage_endkunden_intern_templates.xml',
        # 17.0 attrs/states incompatibility — disabled for now
        # 'views/crm_lead_views.xml',
        'views/website_menu.xml',
    ],
    # The premium_car_crm_extension module should be installed manually
    'installable': True,
    'application': True,
}
