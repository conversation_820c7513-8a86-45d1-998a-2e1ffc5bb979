import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)

def migrate(cr, version):
    """
    Migration script to set company_id for existing insurance records
    based on their related CRM lead's policy provider.
    Updated for v1.6.68: Fix company-based access control issues.
    """
    _logger.info("Starting migration to set company_id for existing insurance records (v1.6.68 fix)")

    env = api.Environment(cr, SUPERUSER_ID, {})

    # List of models to migrate
    models_to_migrate = [
        'premium.car.request',
        'premiumcar.untervermittler',
        'premiumcar.endkunden',
        'premiumcar.erstanfrage.endkunden.intern',
        'augencheck.request'
    ]

    for model_name in models_to_migrate:
        try:
            _logger.info(f"Migrating {model_name} records...")

            # Check if model exists
            if model_name not in env:
                _logger.warning(f"Model {model_name} not found, skipping...")
                continue

            model = env[model_name]

            # Find records without company_id set
            records_without_company = model.search([
                ('company_id', '=', False)
            ])

            _logger.info(f"Found {len(records_without_company)} {model_name} records without company_id")

            updated_count = 0
            default_company_count = 0

            for record in records_without_company:
                company_id = None

                # Try to get company from related CRM lead's policy provider
                if hasattr(record, 'lead_id') and record.lead_id:
                    if hasattr(record.lead_id, 'policy_provider_id') and record.lead_id.policy_provider_id:
                        company_id = record.lead_id.policy_provider_id.id
                        _logger.debug(f"Setting company_id to {record.lead_id.policy_provider_id.name} for {model_name} record {record.id}")

                # If no company found from lead, use default company
                if not company_id:
                    default_company = env['res.company'].search([], limit=1)
                    if default_company:
                        company_id = default_company.id
                        default_company_count += 1
                        _logger.debug(f"Setting default company_id to {default_company.name} for {model_name} record {record.id}")

                # Update the record
                if company_id:
                    try:
                        record.write({'company_id': company_id})
                        updated_count += 1
                    except Exception as e:
                        _logger.error(f"Error updating {model_name} record {record.id}: {e}")

            _logger.info(f"Updated {updated_count} {model_name} records")
            _logger.info(f"Set to default company: {default_company_count} records")

        except Exception as e:
            _logger.error(f"Error migrating {model_name}: {e}")
            continue

    # Additional check for v1.6.68: Verify all records have valid company_id
    _logger.info("=== v1.6.68 Additional Checks ===")

    # Get all existing companies
    all_companies = env['res.company'].search([])
    valid_company_ids = all_companies.ids
    _logger.info(f"Valid company IDs in system: {valid_company_ids}")

    for model_name in models_to_migrate:
        try:
            if model_name not in env:
                continue

            model = env[model_name]

            # Check for records with invalid company references
            invalid_records = model.search([
                ('company_id', 'not in', valid_company_ids),
                ('company_id', '!=', False)
            ])

            if invalid_records:
                _logger.warning(f"Found {len(invalid_records)} {model_name} records with invalid company_id")
                for record in invalid_records:
                    try:
                        record.write({'company_id': default_company.id})
                        _logger.info(f"Fixed invalid company_id for {model_name} record {record.id}")
                    except Exception as e:
                        _logger.error(f"Error fixing {model_name} record {record.id}: {e}")

            # Final verification
            total_records = model.search_count([])
            records_with_company = model.search_count([('company_id', '!=', False)])
            _logger.info(f"{model_name}: {records_with_company}/{total_records} records have company_id set")

        except Exception as e:
            _logger.error(f"Error in additional checks for {model_name}: {e}")

    _logger.info("Migration completed successfully - company-based access control should now work properly")
