import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)

def migrate(cr, version):
    """
    CRITICAL FIX: Clear cached action definitions that contain problematic domain filters.
    This migration safely removes the cached problematic domains from the database.
    """
    _logger.info("Starting CRITICAL migration to clear cached action domains (v1.6.81)")

    env = api.Environment(cr, SUPERUSER_ID, {})

    try:
        # List of action XML IDs that need domain clearing
        action_xml_ids = [
            'InsuranceForm.action_premium_car_request',
            'InsuranceForm.action_premiumcar_untervermittler', 
            'InsuranceForm.action_premiumcar_endkunden',
            'InsuranceForm.action_premiumcar_erstanfrage_endkunden_intern',
            'InsuranceForm.action_augencheck_request'
        ]

        # Clear problematic domains from cached actions
        for xml_id in action_xml_ids:
            try:
                # Get the action record from database
                action_ref = env.ref(xml_id, raise_if_not_found=False)
                if action_ref:
                    _logger.info(f"Processing action: {xml_id}")
                    
                    # Check if action has a problematic domain
                    if hasattr(action_ref, 'domain') and action_ref.domain:
                        domain_str = str(action_ref.domain)
                        if 'company_id' in domain_str and ('=' in domain_str or 'in' in domain_str):
                            _logger.warning(f"Found problematic domain in {xml_id}: {action_ref.domain}")
                            
                            # Clear the problematic domain
                            action_ref.write({'domain': False})
                            _logger.info(f"✅ Cleared problematic domain for {xml_id}")
                        else:
                            _logger.info(f"✅ Action {xml_id} domain is clean")
                    else:
                        _logger.info(f"✅ Action {xml_id} has no domain")
                        
                else:
                    _logger.warning(f"❌ Action not found: {xml_id}")
                    
            except Exception as e:
                _logger.error(f"❌ Error processing action {xml_id}: {e}")
                continue

        # Also clear any record rules that might be cached
        try:
            _logger.info("Clearing any cached record rules...")
            
            # Find and disable any problematic record rules
            rule_names = [
                'Premium Car Request: Company Access V2',
                'Premium Car Untervermittler: Company Access V2', 
                'Premium Car Endkunden: Company Access V2',
                'Premium Car Erstanfrage Endkunden Intern: Company Access V2',
                'Augencheck Request: Company Access V2'
            ]
            
            for rule_name in rule_names:
                rules = env['ir.rule'].search([('name', '=', rule_name)])
                if rules:
                    _logger.info(f"Disabling rule: {rule_name}")
                    rules.write({'active': False})
                    
        except Exception as e:
            _logger.error(f"Error clearing record rules: {e}")

        # Force clear all caches
        try:
            _logger.info("Force clearing all caches...")
            env.registry.clear_cache()
            env['ir.actions.act_window'].clear_caches()
            _logger.info("✅ All caches cleared")
        except Exception as e:
            _logger.error(f"Error clearing caches: {e}")

        _logger.info("✅ CRITICAL migration completed - JavaScript errors should be resolved")

    except Exception as e:
        _logger.error(f"❌ CRITICAL migration failed: {e}")
        # Don't raise - we want the module to still work even if migration fails
        pass
