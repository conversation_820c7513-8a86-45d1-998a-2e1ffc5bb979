from odoo import models, fields, api, _
import logging
import traceback
from odoo.exceptions import UserError
from psycopg2.errors import InFailedSqlTransaction
import base64
import datetime

_logger = logging.getLogger(__name__)

# Check if ReportLab is available
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, BaseDocTemplate, PageTemplate, Frame, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_LEFT
    import io
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    _logger.warning("ReportLab is not available. PDF generation will be disabled.")

class PremiumCarRequest(models.Model):
    _name = 'premium.car.request'
    _description = 'PremiumCar Erstanfrage Untervermittler'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    # Basic fields
    name = fields.Char(string='Name', default='New', tracking=True)

    # Contact information
    salutation = fields.Selection([
        ('mr', 'Herr'),
        ('mrs', 'Frau'),
        ('diverse', 'Divers'),
        ('company', 'Firma'),
        ('other', 'Sonstige')
    ], string='Anrede', tracking=True)
    first_name = fields.Char(string='Vorname', tracking=True)
    last_name = fields.Char(string='Nachname', tracking=True)
    birth_date = fields.Date(string='Geburtsdatum', tracking=True)
    street_number = fields.Char(string='Hausnummer', tracking=True)
    street_addition = fields.Char(string='Adresszusatz', tracking=True)
    zip = fields.Char(string='PLZ', tracking=True)
    city = fields.Char(string='Ort', tracking=True)
    country_id = fields.Many2one('res.country', string='Land', tracking=True)
    phone = fields.Char(string='Telefon', tracking=True)
    email = fields.Char(string='E-Mail', tracking=True)
    contact_phone = fields.Char(string='Contact Phone', tracking=True)
    contact_email = fields.Char(string='Contact Email', tracking=True)
    has_customer_authority = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Kundenvollmacht vorhanden', tracking=True)

    # Status tracking
    state = fields.Selection([
        ('draft', 'Entwurf'),
        ('submitted', 'Eingereicht'),
        ('processed', 'Bearbeitet')
    ], default='draft', string='Status', tracking=True)

    # Link to CRM Lead
    lead_id = fields.Many2one('crm.lead', string='Related Lead', ondelete='set null', tracking=True)

    # Link to Contact
    partner_id = fields.Many2one('res.partner', string='Related Contact', ondelete='set null', tracking=True)

    # Company field for multi-company access control
    company_id = fields.Many2one('res.company', string='Company',
                                default=lambda self: self.env.company,
                                required=True,
                                help="Company that this request belongs to")

    # SQL constraints
    _sql_constraints = [
        ('company_id_required', 'CHECK (company_id IS NOT NULL)',
         'Company is required for all insurance requests.'),
    ]

    @api.model
    def search(self, args, offset=0, limit=None, order=None, count=False):
        """Override search to debug record rules"""
        _logger.info(f"=== DEBUGGING PREMIUM CAR REQUEST SEARCH ===")
        _logger.info(f"User: {self.env.user.name} (ID: {self.env.user.id})")
        _logger.info(f"Current company: {self.env.company.name} (ID: {self.env.company.id})")
        _logger.info(f"User companies: {[c.name for c in self.env.user.company_ids]}")
        _logger.info(f"Search args: {args}")

        # Check if record rules are active
        rules = self.env['ir.rule'].sudo().search([
            ('model_id.model', '=', 'premium.car.request'),
            ('active', '=', True)
        ])
        _logger.info(f"Active record rules for premium.car.request: {[(r.name, r.domain_force) for r in rules]}")

        result = super().search(args, offset=offset, limit=limit, order=order, count=count)
        _logger.info(f"Search result count: {len(result) if not count else result}")
        _logger.info(f"=== END DEBUGGING ===")
        return result

    @api.model
    def web_search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        """Override web_search_read to debug record rules (this is what the web interface calls)"""
        _logger.info(f"=== DEBUGGING PREMIUM CAR REQUEST WEB_SEARCH_READ ===")
        _logger.info(f"User: {self.env.user.name} (ID: {self.env.user.id})")
        _logger.info(f"Current company: {self.env.company.name} (ID: {self.env.company.id})")
        _logger.info(f"User companies: {[c.name for c in self.env.user.company_ids]}")
        _logger.info(f"Domain: {domain}")

        # Check if record rules are active
        rules = self.env['ir.rule'].sudo().search([
            ('model_id.model', '=', 'premium.car.request'),
            ('active', '=', True)
        ])
        _logger.info(f"Active record rules for premium.car.request: {[(r.name, r.domain_force) for r in rules]}")

        result = super().web_search_read(domain=domain, fields=fields, offset=offset, limit=limit, order=order)
        _logger.info(f"Web search result count: {result.get('length', 0) if isinstance(result, dict) else len(result)}")
        _logger.info(f"=== END WEB_SEARCH_READ DEBUGGING ===")
        return result

    # Broker Information
    broker_salutation = fields.Selection([
        ('mr', 'Herr'),
        ('mrs', 'Frau'),
        ('diverse', 'Divers'),
        ('company', 'Firma'),
        ('other', 'Sonstige')
    ], string='Anrede (Vermittler)')

    # Vehicle Information
    vehicle_manufacturer = fields.Many2one('vehicle.manufacturer', string="Fahrzeughersteller")
    vehicle_type = fields.Char(string="Fahrzeugtyp")
    vehicle_value = fields.Float(string="Fahrzeugwert")
    license_plate = fields.Char(string="Kennzeichen")
    first_registration = fields.Date(string="Erstzulassung")
    registration_on_policyholder = fields.Date(string="Zulassung auf VN")
    current_milage = fields.Integer(string="Aktueller km-Stand")
    annual_milage = fields.Integer(string="Jahresfahrleistung")

    # Insurance Information
    insurance_start_date = fields.Date(string="Versicherungsbeginn")
    seasonal_registration = fields.Boolean(string="Saisonzulassung")
    seasonal_period_from = fields.Date(string="Saisonzeitraum von")
    seasonal_period_to = fields.Date(string="Saisonzeitraum bis")
    input_tax_deduction_entitlement = fields.Boolean(string="Vorsteuerabzugsberechtigung")
    payment_type_for_vehicle = fields.Selection([
        ('leasing', 'Leasing'),
        ('financing', 'Finanzierung'),
        ('cash_payment', 'Barzahlung')
    ], string="Zahlart")
    usage_purpose = fields.Selection([
        ('mainly_private', 'Überwiegend privat'),
        ('mainly_professional', 'Überwiegend beruflich'),
        ('only_private', 'Nur privat'),
        ('only_professional', 'Nur beruflich')
    ], string="Verwendungszweck")
    existing_insurance = fields.Boolean(string="Vorversicherung")
    previous_insurer_and_policy_number = fields.Char(string="Vorversicherer und Policen-Nr")
    previous_claims = fields.Boolean(string="Vorschäden vorhanden")

    # Driver Information
    driver_1 = fields.Char(string="Fahrer 1")
    dob_driver_1 = fields.Date(string="Geburtsdatum Fahrer 1")
    driver_2 = fields.Char(string="Fahrer 2")
    dob_driver_2 = fields.Date(string="Geburtsdatum Fahrer 2")
    driver_3 = fields.Char(string="Fahrer 3")
    dob_driver_3 = fields.Date(string="Geburtsdatum Fahrer 3")
    driver_4 = fields.Char(string="Fahrer 4")
    dob_driver_4 = fields.Date(string="Geburtsdatum Fahrer 4")

    # Security and Risk Information
    risk_requirement = fields.Selection([
        ('single_garage', 'Einzel-/Doppelgarage'),
        ('communal_garage', 'Sammelgarage/Halle'),
        ('no_garage', 'keine abschließbare, überdachte und durch feste Wände umschlossene Abstellmöglichkeit')
    ], string="Risikoannahmevoraussetzung")
    tracker = fields.Boolean(string="Tracker vorhanden")
    burglary_alarm_system = fields.Selection([
        ('no_burglary_alarm_system', 'Keine Einbruchmeldeanlage'),
        ('burglary_alarm_system', 'Einbruchmeldeanlage vorhanden'),
        ('connected_burglary_alarm_system', 'Aufgeschaltete Einbruchmeldeanlage')
    ], string="Einbruchmeldeanlage")
    fire_alarm_system = fields.Selection([
        ('no_fire_alarm_system', 'Keine Brandmeldeanlage'),
        ('fire_alarm_system', 'Brandmeldeanlage vorhanden'),
        ('connected_fire_alarm_system', 'Aufgeschaltete Brandmeldeanlage')
    ], string="Brandmeldeanlage")

    # Vehicle Owner Information
    divergent_vehicle_owner = fields.Boolean(string="Halter abweichend vom VN")
    owning_comparable_vehicles = fields.Boolean(string="Besitz vergleichbarer Fahrzeuge")
    driving_experience_with_comparable_vehicle = fields.Boolean(string="Fahrerfahrung mit vergleichbaren Fahrzeugen")
    main_vehicle_location_at_policyholder_address = fields.Boolean(string="Fahrzeugstandort bei VN")
    license_plate_of_daily_use_vehicle = fields.Char(string="Kennzeichen Alltagsfahrzeug")

    # Additional Information
    other_information = fields.Text(string="Weitere Informationen")

    # Override to combine name
    @api.onchange('first_name', 'last_name')
    def _compute_contact_name(self):
        for record in self:
            if record.first_name or record.last_name:
                record.contact_name = f"{record.first_name or ''} {record.last_name or ''}".strip()

    def action_submit(self):
        """Submit the request and update its status with improved error handling"""
        cr = self.env.cr

        for rec in self:
            try:
                # Create a savepoint before updating state
                cr.execute('SAVEPOINT premiumcar_submit_savepoint')

                # Update state
                rec.state = 'submitted'
                _logger.info(f"Updated PremiumCar request {rec.id} state to 'submitted'")

                # Ensure lead is updated with latest information
                if rec.lead_id:
                    try:
                        # Create a savepoint before updating lead
                        cr.execute('SAVEPOINT premiumcar_lead_update_savepoint')

                        # Update lead with current values
                        lead_vals = {
                            'contact_name': f"{rec.first_name} {rec.last_name}".strip(),
                            'email_from': rec.contact_email,
                            'phone': rec.contact_phone,
                        }

                        # Update the lead
                        rec.lead_id.sudo().write(lead_vals)
                        _logger.info(f"Updated CRM lead {rec.lead_id.id} with latest information")
                    except Exception as e:
                        _logger.error(f"Error updating CRM lead from PremiumCar request: {e}")
                        _logger.error(f"Traceback: {traceback.format_exc()}")
                        # Rollback to savepoint to avoid transaction issues
                        cr.execute('ROLLBACK TO SAVEPOINT premiumcar_lead_update_savepoint')
                        # Continue without updating lead
                        _logger.info("Continuing without updating lead due to error")
            except Exception as e:
                _logger.error(f"Error submitting PremiumCar request: {e}")
                _logger.error(f"Traceback: {traceback.format_exc()}")
                # Rollback to savepoint
                cr.execute('ROLLBACK TO SAVEPOINT premiumcar_submit_savepoint')
                # Raise user-friendly error
                raise UserError(_("Could not submit PremiumCar request. Please try again or contact support."))

    def action_process(self):
        """Mark the request as processed with improved error handling"""
        cr = self.env.cr

        for rec in self:
            try:
                # Create a savepoint before updating state
                cr.execute('SAVEPOINT premiumcar_process_savepoint')

                # Update state
                rec.state = 'processed'
                _logger.info(f"Updated PremiumCar request {rec.id} state to 'processed'")

                # Convert lead to opportunity if it exists
                if rec.lead_id and rec.lead_id.type == 'lead':
                    try:
                        # Create a savepoint before converting lead
                        cr.execute('SAVEPOINT premiumcar_lead_convert_savepoint')

                        # First, check which fields actually exist in the model
                        lead_fields = rec.lead_id.fields_get()
                        valid_fields = {}

                        # Initialize empty mandatory fields dictionary
                        mandatory_fields = {}

                        # Check for birth date field and add it if it exists
                        if rec.birth_date:
                            if 'birth_date' in lead_fields:
                                mandatory_fields['birth_date'] = rec.birth_date
                            elif 'policy_holder_dob' in lead_fields:
                                mandatory_fields['policy_holder_dob'] = rec.birth_date
                            elif 'dob_policy_holder' in lead_fields:
                                mandatory_fields['dob_policy_holder'] = rec.birth_date
                        else:
                            # Fallback to today if no birth date is set
                            if 'birth_date' in lead_fields:
                                mandatory_fields['birth_date'] = fields.Date.today()
                            elif 'policy_holder_dob' in lead_fields:
                                mandatory_fields['policy_holder_dob'] = fields.Date.today()
                            elif 'dob_policy_holder' in lead_fields:
                                mandatory_fields['dob_policy_holder'] = fields.Date.today()

                        # Check for gender field and add it if it exists
                        gender = 'other'  # Default
                        if rec.salutation == 'mr':
                            gender = 'male'
                        elif rec.salutation == 'mrs':
                            gender = 'female'

                        if 'gender' in lead_fields:
                            mandatory_fields['gender'] = gender
                        elif 'policy_holder_gender' in lead_fields:
                            mandatory_fields['policy_holder_gender'] = gender

                        # Check for policy category field and add it if it exists
                        if 'policy_category' in lead_fields:
                            mandatory_fields['policy_category'] = 'business'
                        elif 'insurance_category_id' in lead_fields:
                            # Try to find the business insurance category
                            try:
                                business_category = self.env['insurance.category'].sudo().search(
                                    [('category', '=', 'business')], limit=1)
                                if business_category:
                                    mandatory_fields['insurance_category_id'] = business_category.id
                            except Exception as cat_e:
                                _logger.warning(f"Could not set insurance category: {cat_e}")

                        # Update the lead with mandatory fields before conversion
                        if mandatory_fields:
                            try:
                                rec.lead_id.sudo().write(mandatory_fields)
                                _logger.info(f"Updated lead {rec.lead_id.id} with mandatory fields: {mandatory_fields}")
                            except Exception as mand_e:
                                _logger.error(f"Error updating lead with mandatory fields: {mand_e}")
                                _logger.error(f"Traceback: {traceback.format_exc()}")

                        # Try to convert the lead to opportunity
                        try:
                            # Check if the convert_opportunity method exists
                            if hasattr(rec.lead_id, 'convert_opportunity'):
                                # Convert to opportunity
                                rec.lead_id.sudo().convert_opportunity(rec.partner_id.id if rec.partner_id else False)
                                _logger.info(f"Converted lead {rec.lead_id.id} to opportunity")
                            else:
                                _logger.warning(f"convert_opportunity method not found on lead {rec.lead_id.id}")
                        except Exception as conv_e:
                            _logger.error(f"Error converting lead to opportunity: {conv_e}")
                            _logger.error(f"Traceback: {traceback.format_exc()}")
                    except Exception as e:
                        _logger.error(f"Error processing lead conversion: {e}")
                        _logger.error(f"Traceback: {traceback.format_exc()}")
                        # Rollback to savepoint
                        cr.execute('ROLLBACK TO SAVEPOINT premiumcar_lead_convert_savepoint')
                        # Continue without converting lead
                        _logger.info("Continuing without converting lead due to error")
            except Exception as e:
                _logger.error(f"Error processing PremiumCar request: {e}")
                _logger.error(f"Traceback: {traceback.format_exc()}")
                # Rollback to savepoint
                cr.execute('ROLLBACK TO SAVEPOINT premiumcar_process_savepoint')
                # Raise user-friendly error
                raise UserError(_("Could not process PremiumCar request. Please try again or contact support."))

    # Override to handle CRM lead and contact creation
    @api.model
    def create(self, vals):
        """
        Create a new PremiumCar request with improved error handling and transaction management.
        This method handles the creation of related partner and CRM lead records.
        """
        # Use a savepoint to allow partial rollback if needed
        cr = self.env.cr

        try:
            # Generate sequence number if needed
            if vals.get('name', 'New') == 'New':
                vals['name'] = self.env['ir.sequence'].next_by_code('premium.car.request') or 'Premium Car Request'

            # Create a contact record for this customer
            timestamp = fields.Datetime.now().strftime('%Y%m%d%H%M%S')
            reference_code = f"PC-{timestamp}"

            # Use clean name without timestamp
            clean_name = f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip()
            if not clean_name:
                _logger.warning("Missing first_name or last_name for contact creation")
                clean_name = f"Unknown Contact ({timestamp})"

            partner_vals = {
                'name': clean_name,
                'ref': reference_code,
                'street': vals.get('street_addition', ''),
                'street2': vals.get('street_number', ''),
                'zip': vals.get('zip', ''),
                'city': vals.get('city', ''),
                'country_id': vals.get('country_id'),
                'email': vals.get('contact_email', ''),
                'phone': vals.get('contact_phone', ''),
                'type': 'contact',
                'company_type': 'person',
                'comment': f"Created from PremiumCar Erstanfrage Untervermittler form {vals.get('name', 'New')}",
            }

            # Set the proper title based on salutation
            if vals.get('salutation') == 'mr':
                title = self.env.ref('base.res_partner_title_mister', False)
                if title:
                    partner_vals['title'] = title.id
            elif vals.get('salutation') == 'mrs':
                title = self.env.ref('base.res_partner_title_madam', False)
                if title:
                    partner_vals['title'] = title.id

            # Create a savepoint before partner operations
            cr.execute('SAVEPOINT premiumcar_partner_savepoint')

            # Check if a partner with this email or phone already exists
            existing_partner = False
            if partner_vals.get('email'):
                existing_partner = self.env['res.partner'].sudo().search([
                    ('email', '=', partner_vals.get('email'))
                ], limit=1)
                if existing_partner:
                    _logger.info(f"Found existing partner with email {partner_vals.get('email')}: ID {existing_partner.id}")

            if not existing_partner and partner_vals.get('phone'):
                existing_partner = self.env['res.partner'].sudo().search([
                    ('phone', '=', partner_vals.get('phone'))
                ], limit=1)
                if existing_partner:
                    _logger.info(f"Found existing partner with phone {partner_vals.get('phone')}: ID {existing_partner.id}")

            # Create or link the partner
            try:
                if existing_partner:
                    # Use existing partner
                    _logger.info(f"Using existing partner with ID {existing_partner.id} for PremiumCar form")
                    partner_id = existing_partner.id
                else:
                    # Create new partner
                    partner = self.env['res.partner'].with_context(disable_duplicate_check=True).sudo().create(partner_vals)
                    partner_id = partner.id
                    _logger.info(f"Created new partner with ID {partner_id} for PremiumCar form")

                # Set the partner_id in the record
                vals['partner_id'] = partner_id
            except Exception as e:
                _logger.error(f"Error creating contact for PremiumCar form: {e}")
                _logger.error(f"Traceback: {traceback.format_exc()}")
                # Rollback to savepoint to avoid transaction issues
                cr.execute('ROLLBACK TO SAVEPOINT premiumcar_partner_savepoint')
                # Continue without partner
                _logger.info("Continuing without partner due to error")

            # Create a savepoint before lead operations
            cr.execute('SAVEPOINT premiumcar_lead_savepoint')

            # Create CRM Lead if none exists
            if not vals.get('lead_id'):
                try:
                    # Prepare data for mapper
                    form_data = {
                        'first_name': vals.get('first_name'),
                        'last_name': vals.get('last_name'),
                        'email': vals.get('contact_email'),
                        'phone': vals.get('contact_phone'),
                        # Add address information
                        'street_number': vals.get('street_number'),
                        'street_addition': vals.get('street_addition'),
                        'zip': vals.get('zip'),
                        'city': vals.get('city'),
                        'country_id': vals.get('country_id'),
                        # Add salutation for gender mapping
                        'salutation': vals.get('salutation'),
                        # Add birth date
                        'birth_date': vals.get('birth_date'),
                    }

                    # Check if opportunity mapper exists
                    mapper_exists = False
                    try:
                        mapper_exists = self.env['opportunity.mapper'] and True
                        _logger.info("opportunity.mapper model is available")
                    except Exception as mapper_e:
                        _logger.error(f"Error checking for opportunity.mapper model: {mapper_e}")

                    # Prepare lead values
                    full_name = f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip()

                    if mapper_exists:
                        try:
                            # Use mapper to map fields
                            _logger.info(f"Attempting to map form data to opportunity with mapper: {form_data}")
                            lead_vals = self.env['opportunity.mapper'].map_form_to_opportunity(form_data, 'premiumcar')
                            _logger.info(f"Successfully mapped form data to lead values: {lead_vals}")

                            # Set lead name if not set by mapper
                            if not lead_vals.get('name'):
                                lead_vals['name'] = f"PremiumCar Erstanfrage von {full_name}"

                            # Add reference code to website_ref_number field
                            if reference_code:
                                lead_vals['website_ref_number'] = reference_code
                                # Also add to description for backward compatibility
                                if lead_vals.get('description'):
                                    lead_vals['description'] = f"Reference Number: {reference_code}\n\n" + lead_vals['description']
                                else:
                                    lead_vals['description'] = f"Reference Number: {reference_code}"
                                _logger.info(f"Added reference code {reference_code} to website_ref_number field")
                        except Exception as mapper_e:
                            _logger.error(f"Error using opportunity mapper: {mapper_e}")
                            _logger.error(f"Traceback: {traceback.format_exc()}")

                            # Fallback to basic lead creation
                            _logger.info("Falling back to basic lead creation")

                            # Map salutation to gender with improved validation
                            crm_fields = self.env['crm.lead'].sudo().fields_get()

                            # Basic lead values that are standard in Odoo
                            lead_vals = {
                                'name': f"PremiumCar Erstanfrage von {full_name}",
                                'type': 'lead',
                                'contact_name': full_name,
                                'email_from': vals.get('contact_email', ''),
                                'phone': vals.get('contact_phone', ''),
                                'description': f"PremiumCar Erstanfrage von {full_name}",
                            }

                            # Handle gender mapping with validation
                            gender_field = None
                            if 'gender' in crm_fields:
                                gender_field = 'gender'
                            elif 'policy_holder_gender' in crm_fields:
                                gender_field = 'policy_holder_gender'

                            if gender_field:
                                # Get valid selection options
                                field_selection = crm_fields.get(gender_field, {}).get('selection', [])
                                field_options = [opt[0] for opt in field_selection] if field_selection else []
                                _logger.debug(f"Available {gender_field} options: {field_options}")

                                # Map salutation to gender
                                if vals.get('salutation') == 'mr':
                                    gender_value = 'male'
                                elif vals.get('salutation') == 'mrs':
                                    gender_value = 'female'
                                else:
                                    # For other salutations, try different possible values
                                    if 'other' in field_options:
                                        gender_value = 'other'
                                    elif 'others' in field_options:
                                        gender_value = 'others'
                                    elif 'diverse' in field_options:
                                        gender_value = 'diverse'
                                    else:
                                        # Use the first available option as fallback
                                        gender_value = field_options[0] if field_options else 'other'
                                        _logger.warning(f"No suitable gender option found for salutation '{vals.get('salutation')}', using fallback: '{gender_value}'")

                                # Only set if the value is valid
                                if gender_value in field_options:
                                    lead_vals[gender_field] = gender_value
                                    _logger.debug(f"Set {gender_field} to: {gender_value}")
                                else:
                                    _logger.warning(f"Gender value '{gender_value}' not available in {gender_field} options: {field_options}")

                            # Add birth date field if it exists in the model
                            if 'birth_date' in crm_fields and vals.get('birth_date'):
                                lead_vals['birth_date'] = vals.get('birth_date')
                            elif 'policy_holder_dob' in crm_fields and vals.get('birth_date'):
                                lead_vals['policy_holder_dob'] = vals.get('birth_date')
                            elif 'dob_policy_holder' in crm_fields and vals.get('birth_date'):
                                lead_vals['dob_policy_holder'] = vals.get('birth_date')

                            # Check if policy_category field exists before setting it
                            if 'policy_category' in crm_fields:
                                lead_vals['policy_category'] = 'business'
                            elif 'insurance_category_id' in crm_fields:
                                # Try to find the business insurance category
                                try:
                                    business_category = self.env['insurance.category'].sudo().search(
                                        [('category', '=', 'business')], limit=1)
                                    if business_category:
                                        lead_vals['insurance_category_id'] = business_category.id
                                except Exception as cat_e:
                                    _logger.warning(f"Could not set insurance category: {cat_e}")
                    else:
                        # Fallback to basic lead creation
                        _logger.info("Using basic lead creation (no mapper available)")

                        # Get CRM fields for validation
                        crm_fields = self.env['crm.lead'].sudo().fields_get()

                        # Basic lead values that are standard in Odoo
                        lead_vals = {
                            'name': f"PremiumCar Erstanfrage von {full_name}",
                            'type': 'lead',
                            'contact_name': full_name,
                            'email_from': vals.get('contact_email', ''),
                            'phone': vals.get('contact_phone', ''),
                            'description': f"Reference Number: {reference_code}\n\nPremiumCar Erstanfrage von {full_name}",
                            'website_ref_number': reference_code,  # Add reference number to website_ref_number field
                        }
                        _logger.info(f"Added reference code {reference_code} to website_ref_number field")

                        # Handle gender mapping with validation (same logic as fallback above)
                        gender_field = None
                        if 'gender' in crm_fields:
                            gender_field = 'gender'
                        elif 'policy_holder_gender' in crm_fields:
                            gender_field = 'policy_holder_gender'

                        if gender_field:
                            # Get valid selection options
                            field_selection = crm_fields.get(gender_field, {}).get('selection', [])
                            field_options = [opt[0] for opt in field_selection] if field_selection else []
                            _logger.debug(f"Available {gender_field} options: {field_options}")

                            # Map salutation to gender
                            if vals.get('salutation') == 'mr':
                                gender_value = 'male'
                            elif vals.get('salutation') == 'mrs':
                                gender_value = 'female'
                            else:
                                # For other salutations, try different possible values
                                if 'other' in field_options:
                                    gender_value = 'other'
                                elif 'others' in field_options:
                                    gender_value = 'others'
                                elif 'diverse' in field_options:
                                    gender_value = 'diverse'
                                else:
                                    # Use the first available option as fallback
                                    gender_value = field_options[0] if field_options else 'other'
                                    _logger.warning(f"No suitable gender option found for salutation '{vals.get('salutation')}', using fallback: '{gender_value}'")

                            # Only set if the value is valid
                            if gender_value in field_options:
                                lead_vals[gender_field] = gender_value
                                _logger.debug(f"Set {gender_field} to: {gender_value}")
                            else:
                                _logger.warning(f"Gender value '{gender_value}' not available in {gender_field} options: {field_options}")

                        # Add birth date field if it exists in the model
                        if 'birth_date' in crm_fields and vals.get('birth_date'):
                            lead_vals['birth_date'] = vals.get('birth_date')
                        elif 'policy_holder_dob' in crm_fields and vals.get('birth_date'):
                            lead_vals['policy_holder_dob'] = vals.get('birth_date')
                        elif 'dob_policy_holder' in crm_fields and vals.get('birth_date'):
                            lead_vals['dob_policy_holder'] = vals.get('birth_date')

                        # Check if policy_category field exists before setting it
                        if 'policy_category' in crm_fields:
                            lead_vals['policy_category'] = 'business'
                        elif 'insurance_category_id' in crm_fields:
                            # Try to find the business insurance category
                            try:
                                business_category = self.env['insurance.category'].sudo().search(
                                    [('category', '=', 'business')], limit=1)
                                if business_category:
                                    lead_vals['insurance_category_id'] = business_category.id
                            except Exception as cat_e:
                                _logger.warning(f"Could not set insurance category: {cat_e}")

                    # Link to partner if created
                    if vals.get('partner_id'):
                        lead_vals['partner_id'] = vals.get('partner_id')
                        _logger.info(f"Linking lead to partner ID: {vals.get('partner_id')}")

                    # Ensure required fields are present
                    if not lead_vals.get('name'):
                        lead_vals['name'] = f"PremiumCar Erstanfrage von {full_name}"

                    if not lead_vals.get('type'):
                        lead_vals['type'] = 'lead'

                    # Log the final lead values before creation
                    _logger.info(f"Final lead values for creation: {lead_vals}")

                    # Create lead with extensive diagnostic logging that will be visible in the UI
                    debug_info = f"About to create CRM lead with values: {lead_vals}\n"

                    # Create a note in the chatter that will be visible in the UI
                    self = self.with_context(tracking_disable=True)  # Disable tracking temporarily

                    # Check if we have access to create CRM leads
                    try:
                        can_create = self.env['crm.lead'].check_access_rights('create', raise_exception=False)
                        debug_info += f"User has create access rights for crm.lead: {can_create}\n"
                    except Exception as access_e:
                        debug_info += f"Error checking access rights: {access_e}\n"

                    # Check if all required fields are present
                    try:
                        crm_fields = self.env['crm.lead'].fields_get()
                        required_fields = [f for f, attrs in crm_fields.items()
                                          if attrs.get('required') and not attrs.get('readonly')]
                        missing_fields = [f for f in required_fields if f not in lead_vals]
                        if missing_fields:
                            debug_info += f"Missing required fields for crm.lead: {missing_fields}\n"
                            # Try to add default values for missing required fields
                            for field in missing_fields:
                                if field == 'name' and not lead_vals.get('name'):
                                    lead_vals['name'] = f"PremiumCar Erstanfrage {fields.Datetime.now()}"
                                elif field == 'type' and not lead_vals.get('type'):
                                    lead_vals['type'] = 'lead'
                    except Exception as fields_e:
                        debug_info += f"Error checking required fields: {fields_e}\n"

                    # Log to standard logger as well
                    _logger.info(debug_info)

                    # Try to create the lead with detailed error logging
                    try:
                        lead = self.env['crm.lead'].sudo().create(lead_vals)
                        vals['lead_id'] = lead.id
                        debug_info += f"Successfully created CRM lead with ID {lead.id} for PremiumCar form\n"

                        # Set company_id from the created lead's policy provider
                        if lead.policy_provider_id:
                            vals['company_id'] = lead.policy_provider_id.id
                            _logger.info(f"Set company_id to {lead.policy_provider_id.name} from CRM lead")

                        # Force a commit to ensure the lead is saved
                        self.env.cr.commit()
                    except Exception as create_e:
                        error_msg = str(create_e)
                        debug_info += f"CRITICAL ERROR creating CRM lead: {error_msg}\n"
                        debug_info += f"Detailed error traceback: {traceback.format_exc()}\n"

                        # Try to identify specific error type
                        if "access right" in error_msg.lower() or "access error" in error_msg.lower():
                            debug_info += "This appears to be a permissions issue\n"
                        elif "null value in column" in error_msg.lower():
                            debug_info += "This appears to be a missing required field issue\n"
                        elif "foreign key constraint" in error_msg.lower():
                            debug_info += "This appears to be a foreign key constraint issue\n"

                        # Log the final error information
                        _logger.error(debug_info)

                        # Re-raise to be caught by the outer exception handler
                        raise
                    finally:
                        # Add the debug info to the record's chatter for UI visibility
                        try:
                            # This will be visible in the UI even without server log access
                            message = f"<pre>{debug_info}</pre>"
                            self.env['mail.message'].sudo().create({
                                'body': message,
                                'message_type': 'comment',
                                'model': 'premium.car.request',
                                'res_id': self.id if self.id else 0,  # Will be set later if not yet created
                                'subtype_id': self.env.ref('mail.mt_note').id,
                                'author_id': self.env.user.partner_id.id,
                            })
                        except Exception as msg_e:
                            _logger.error(f"Could not create debug message: {msg_e}")
                except Exception as e:
                    _logger.error(f"Error creating CRM lead from PremiumCar request: {e}")
                    _logger.error(f"Traceback: {traceback.format_exc()}")
                    # Rollback to savepoint to avoid transaction issues
                    cr.execute('ROLLBACK TO SAVEPOINT premiumcar_lead_savepoint')
                    # Continue without lead
                    _logger.info("Continuing without lead due to error")

            # If we have a lead_id but no company_id, try to get it from the existing lead
            if vals.get('lead_id') and not vals.get('company_id'):
                try:
                    lead = self.env['crm.lead'].sudo().browse(vals['lead_id'])
                    if lead.exists() and lead.policy_provider_id:
                        vals['company_id'] = lead.policy_provider_id.id
                        _logger.info(f"Set company_id to {lead.policy_provider_id.name} from existing CRM lead")
                except Exception as e:
                    _logger.error(f"Error getting company from existing CRM lead: {e}")

            # Create the PremiumCar request
            try:
                # Remove transient key so super().create() doesn't fail
                vals.pop('reference_code', None)
                _logger.info("Removed reference_code key from vals before super().create()")

                result = super(PremiumCarRequest, self).create(vals)
                _logger.info(f"Successfully created PremiumCar request with ID {result.id}")
                return result
            except Exception as e:
                _logger.error(f"Error creating PremiumCar request: {e}")
                _logger.error(f"Traceback: {traceback.format_exc()}")
                # Re-raise the exception to show error to user
                raise

        except Exception as e:
            _logger.error(f"Unexpected error in PremiumCar request creation: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            # Re-raise with a more user-friendly message
            raise UserError(_("Could not create PremiumCar request. Please try again or contact support."))

    def generate_pdf_summary(self):
        """Generate a PDF summary of the Premium Car request"""
        self.ensure_one()

        if not REPORTLAB_AVAILABLE:
            _logger.error("ReportLab is not available. Cannot generate PDF.")
            raise UserError(_("PDF generation is not available. Please contact your administrator."))

        try:
            # Create a BytesIO buffer to hold the PDF
            buffer = io.BytesIO()

            # Create the PDF document with custom page template for logo support
            doc = BaseDocTemplate(buffer, pagesize=A4, leftMargin=0.75*inch, rightMargin=0.75*inch)

            # Create a frame for the content
            frame = Frame(0.75*inch, 0.75*inch, A4[0] - 1.5*inch, A4[1] - 1.5*inch,
                         leftPadding=0, bottomPadding=0, rightPadding=0, topPadding=0)

            # Create page template with header/footer function
            page_template = PageTemplate(id='main', frames=[frame], onPage=self._create_header_footer)
            doc.addPageTemplates([page_template])

            story = []

            # Get styles
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=colors.darkblue
            )
            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                spaceBefore=20,
                textColor=colors.darkblue
            )
            normal_style = styles['Normal']

            # Add large spacer to push title below logo and improve page breaks
            story.append(Spacer(1, 80))

            # Title
            story.append(Paragraph("PremiumCar Erstanfrage Untervermittler", title_style))
            story.append(Spacer(1, 20))

            # Reference number - get from CRM lead if available, otherwise use name
            reference_number = self.name
            if self.lead_id and self.lead_id.website_ref_number:
                reference_number = self.lead_id.website_ref_number
            story.append(Paragraph(f"<b>Referenznummer:</b> {reference_number}", normal_style))
            story.append(Spacer(1, 10))

            # Date
            story.append(Paragraph(f"<b>Datum:</b> {datetime.datetime.now().strftime('%d.%m.%Y %H:%M')}", normal_style))
            story.append(Spacer(1, 20))

            # Personal Information
            story.append(Paragraph("Persönliche Informationen", heading_style))
            personal_data = [
                ['Anrede:', self._get_salutation_display()],
                ['Vorname:', self.first_name or ''],
                ['Nachname:', self.last_name or ''],
                ['Geburtsdatum:', self.birth_date.strftime('%d.%m.%Y') if self.birth_date else ''],
                ['E-Mail:', self.contact_email or ''],
                ['Telefon:', self.contact_phone or ''],
            ]
            personal_table = Table(personal_data, colWidths=[2*inch, 3*inch])
            personal_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            story.append(personal_table)
            story.append(Spacer(1, 20))

            # Customer Authority Question
            story.append(Paragraph("Kundenvollmacht", heading_style))
            authority_data = [
                ['Kundenvollmacht vorhanden:', 'Ja' if self.has_customer_authority == 'yes' else 'Nein' if self.has_customer_authority == 'no' else 'Nicht angegeben'],
            ]
            authority_table = Table(authority_data, colWidths=[2*inch, 3*inch])
            authority_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            story.append(authority_table)
            story.append(Spacer(1, 20))

            # Address Information (only if we have address data)
            if any([self.street_number, self.street_addition, self.zip, self.city]):
                story.append(Paragraph("Adressinformationen", heading_style))
                address_data = []
                if self.street_number:
                    address_data.append(['Hausnummer:', self.street_number])
                if self.street_addition:
                    address_data.append(['Zusatz:', self.street_addition])
                if self.zip:
                    address_data.append(['PLZ:', self.zip])
                if self.city:
                    address_data.append(['Stadt:', self.city])
                if self.country_id:
                    address_data.append(['Land:', self.country_id.name])

                if address_data:
                    address_table = Table(address_data, colWidths=[2*inch, 3*inch])
                    address_table.setStyle(TableStyle([
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                    ]))
                    story.append(address_table)
                    story.append(Spacer(1, 20))

            # Vehicle Information (only if we have vehicle data)
            if any([self.vehicle_manufacturer, self.vehicle_type, self.license_plate, self.first_registration, self.vehicle_value]):
                story.append(Paragraph("Fahrzeuginformationen", heading_style))
                vehicle_data = []
                if self.vehicle_manufacturer:
                    vehicle_data.append(['Hersteller:', self.vehicle_manufacturer.name])
                if self.vehicle_type:
                    vehicle_data.append(['Typ:', self.vehicle_type])
                if self.license_plate:
                    vehicle_data.append(['Kennzeichen:', self.license_plate])
                if self.first_registration:
                    vehicle_data.append(['Erstzulassung:', self.first_registration.strftime('%d.%m.%Y')])
                if self.vehicle_value:
                    vehicle_data.append(['Fahrzeugwert:', f"{self.vehicle_value:,.2f} €"])
                if hasattr(self, 'seasonal_registration'):
                    vehicle_data.append(['Saisonzulassung:', 'Ja' if self.seasonal_registration else 'Nein'])

                if vehicle_data:
                    vehicle_table = Table(vehicle_data, colWidths=[2*inch, 3*inch])
                    vehicle_table.setStyle(TableStyle([
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                    ]))
                    story.append(vehicle_table)
                    story.append(Spacer(1, 20))

            # Insurance Information (only if we have actual insurance data from the form)
            insurance_data = []
            if self.insurance_start_date:
                insurance_data.append(['Versicherungsbeginn:', self.insurance_start_date.strftime('%d.%m.%Y')])
            # Only show existing_insurance if it's actually set to True (not just the field exists)
            if hasattr(self, 'existing_insurance') and self.existing_insurance:
                insurance_data.append(['Vorversicherung:', 'Ja'])
            # Only show previous_claims if it's actually set to True (not just the field exists)
            if hasattr(self, 'previous_claims') and self.previous_claims:
                insurance_data.append(['Vorschäden:', 'Ja'])
            if self.usage_purpose:
                insurance_data.append(['Verwendungszweck:', self.usage_purpose])

            if insurance_data:
                story.append(Paragraph("Versicherungsinformationen", heading_style))
                insurance_table = Table(insurance_data, colWidths=[2*inch, 3*inch])
                insurance_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                ]))
                story.append(insurance_table)
                story.append(Spacer(1, 20))

            # Additional notes if any
            if self.other_information:
                story.append(Paragraph("Zusätzliche Informationen", heading_style))
                story.append(Paragraph(self.other_information, normal_style))
                story.append(Spacer(1, 20))

            # Footer
            story.append(Spacer(1, 30))
            footer_text = f"Erstellt am: {fields.Datetime.now().strftime('%d.%m.%Y %H:%M')} Uhr"
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=8,
                textColor=colors.grey,
                alignment=TA_CENTER
            )
            story.append(Paragraph(footer_text, footer_style))

            # Build PDF
            doc.build(story)

            # Get the PDF data
            pdf_data = buffer.getvalue()
            buffer.close()

            # Return base64 encoded PDF
            return base64.b64encode(pdf_data).decode('utf-8')

        except Exception as e:
            _logger.error(f"Error generating PDF for Premium Car request {self.id}: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            raise UserError(_("Error generating PDF. Please try again or contact support."))

    def _get_salutation_display(self):
        """Get display value for salutation"""
        if not self.salutation:
            return ''
        salutation_dict = {
            'mr': 'Herr',
            'mrs': 'Frau',
            'diverse': 'Divers',
            'company': 'Firma',
            'other': 'Sonstige'
        }
        return salutation_dict.get(self.salutation, self.salutation)

    def _get_logo_path(self):

        """Get the path to the logo file for PDF header - conditional based on policy provider"""

        import os

        try:
            _logger.info("=== GETTING LOGO PATH ===")


            # Determine which logo to use based on policy provider
            logo_filename = 'focus_logo.png'  # Default logo

            # Check if we have a related CRM lead with policy provider information
            if self.lead_id and self.lead_id.policy_provider_id:
                policy_provider_name = self.lead_id.policy_provider_id.name
                _logger.info(f"Policy provider found: {policy_provider_name}")

                # Check if policy provider is ARTUS Classic Motors & Masterpieces GmbH
                if policy_provider_name == 'ARTUS Classic Motors & Masterpieces GmbH':
                    logo_filename = 'artus_logo.jpg'
                    _logger.info("Using ARTUS logo based on policy provider")
                else:
                    _logger.info(f"Using default Focus logo for policy provider: {policy_provider_name}")
            else:
                _logger.info("No policy provider found, using default Focus logo")


            # List of possible logo paths to try
            possible_paths = []

            # Method 1: Using __file__ to get current module directory
            try:
                current_dir = os.path.dirname(os.path.abspath(__file__))
                _logger.info(f"Current module directory: {current_dir}")

                # Go up to InsuranceForm directory, then to static/img
                insurance_form_dir = os.path.dirname(current_dir)  # Go up from models/ to InsuranceForm/

                possible_paths.append(os.path.join(insurance_form_dir, 'static', 'img', logo_filename))


            except Exception as e:
                _logger.error(f"Error getting current directory: {e}")

            # Method 2: Using Odoo's get_module_path if available
            try:
                from odoo.modules import get_module_path
                module_path_odoo = get_module_path('InsuranceForm')
                if module_path_odoo:

                    possible_paths.append(os.path.join(module_path_odoo, 'static', 'img', logo_filename))

            except Exception as e:
                _logger.error(f"Error getting module path via Odoo: {e}")

            # Method 3: Try relative paths from common Odoo locations
            try:
                import odoo
                odoo_dir = os.path.dirname(odoo.__file__)
                addons_paths = [

                    os.path.join(odoo_dir, '..', 'addons', 'InsuranceForm', 'static', 'img', logo_filename),
                    os.path.join(odoo_dir, 'addons', 'InsuranceForm', 'static', 'img', logo_filename),

                ]
                possible_paths.extend(addons_paths)
            except Exception as e:
                _logger.error(f"Error getting Odoo addons paths: {e}")


            _logger.info(f"Possible logo paths to check for {logo_filename}: {possible_paths}")


            # Check each path and return the first one that exists
            for path in possible_paths:
                abs_path = os.path.abspath(path)
                _logger.info(f"Checking logo path: {abs_path}")
                if os.path.exists(abs_path):
                    _logger.info(f"Found logo at: {abs_path}")
                    return abs_path
                else:
                    _logger.info(f"Logo not found at: {abs_path}")


            # If the selected logo is not found, try to fallback to focus_logo.png
            if logo_filename != 'focus_logo.png':
                _logger.warning(f"Selected logo {logo_filename} not found, trying fallback to focus_logo.png")
                fallback_paths = []

                # Rebuild paths with focus_logo.png
                try:
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    insurance_form_dir = os.path.dirname(current_dir)
                    fallback_paths.append(os.path.join(insurance_form_dir, 'static', 'img', 'focus_logo.png'))
                except:
                    pass

                try:
                    from odoo.modules import get_module_path
                    module_path_odoo = get_module_path('InsuranceForm')
                    if module_path_odoo:
                        fallback_paths.append(os.path.join(module_path_odoo, 'static', 'img', 'focus_logo.png'))
                except:
                    pass

                # Check fallback paths
                for path in fallback_paths:
                    abs_path = os.path.abspath(path)
                    if os.path.exists(abs_path):
                        _logger.info(f"Found fallback logo at: {abs_path}")
                        return abs_path


            _logger.warning("Logo file not found in any of the expected locations")
            return None

        except Exception as e:
            _logger.error(f"Error getting logo path: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _create_header_footer(self, canvas, doc):
        """Create header with logo and footer for PDF pages"""
        import os
        try:
            _logger.info("=== HEADER FUNCTION CALLED ===")

            # Get logo path
            logo_path = self._get_logo_path()

            if logo_path and os.path.exists(logo_path):
                _logger.info(f"Attempting to draw logo from: {logo_path}")

                # Draw logo in top-right corner with professional sizing
                # Position: 0.4 inch from right edge, 0.25 inch from top (reverted to original position)
                # Title will be positioned below the logo for better visual hierarchy
                logo_width = 2.0 * inch   # Increased from 1.2 to 2.0 inches (67% larger)
                logo_height = 1.0 * inch  # Increased from 0.6 to 1.0 inches (67% larger)

                x_position = A4[0] - 0.4*inch - logo_width  # Right edge minus padding minus logo width
                y_position = A4[1] - 0.25*inch - logo_height  # Top edge minus padding minus logo height (reverted to original)

                _logger.info(f"Drawing logo at position: x={x_position}, y={y_position}, width={logo_width}, height={logo_height}")

                canvas.drawImage(logo_path, x_position, y_position,
                               width=logo_width, height=logo_height,
                               preserveAspectRatio=True, mask='auto')

                _logger.info("Logo successfully drawn to PDF")
            else:
                _logger.info("No valid logo file found, skipping logo in PDF header")

        except Exception as e:
            # Log error but don't fail PDF generation
            _logger.error(f"Error adding logo to PDF: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            pass