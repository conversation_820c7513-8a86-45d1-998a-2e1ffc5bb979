<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_premiumcar_untervermittler_form" model="ir.ui.view">
        <field name="name">premiumcar.untervermittler.form</field>
        <field name="model">premiumcar.untervermittler</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar" options="{'clickable': '1'}"/>
                    <button name="action_view_lead" string="View CRM Lead" type="object" class="oe_highlight" invisible="not lead_id"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group string="Versicherungsinformationen">
                            <field name="insurance_start_date"/>
                            <field name="lead_id" readonly="1"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Vermittler">
                            <group>
                                <group>
                                    <field name="broker_salutation"/>
                                    <field name="broker_first_name"/>
                                    <field name="broker_last_name"/>
                                    <field name="broker_street"/>
                                    <field name="broker_street_number"/>
                                    <field name="broker_street_addition"/>
                                    <field name="broker_zip"/>
                                    <field name="broker_city"/>
                                    <field name="broker_country_id"/>
                                </group>
                                <group>
                                    <field name="broker_email"/>
                                    <field name="broker_phone"/>
                                    <field name="has_customer_authorization"/>
                                </group>
                            </group>
                        </page>
                        <page string="Versicherungsnehmer">
                            <group>
                                <group>
                                    <field name="salutation"/>
                                    <field name="first_name"/>
                                    <field name="last_name"/>
                                    <field name="street"/>
                                    <field name="street_number"/>
                                    <field name="street_addition"/>
                                    <field name="zip"/>
                                    <field name="city"/>
                                    <field name="country_id"/>
                                </group>
                                <group>
                                    <field name="email"/>
                                    <field name="phone"/>
                                    <field name="mobile"/>
                                    <field name="is_owner_different"/>
                                </group>
                            </group>
                            <group invisible="is_owner_different != 'yes'">
                                <group string="Halter Informationen">
                                    <field name="owner_salutation"/>
                                    <field name="owner_first_name"/>
                                    <field name="owner_last_name"/>
                                    <field name="owner_street"/>
                                    <field name="owner_street_number"/>
                                    <field name="owner_street_addition"/>
                                    <field name="owner_zip"/>
                                    <field name="owner_city"/>
                                    <field name="owner_country_id"/>
                                </group>
                                <group string="Kontaktinformationen Halter">
                                    <field name="owner_email"/>
                                    <field name="owner_phone"/>
                                    <field name="owner_mobile"/>
                                </group>
                            </group>
                        </page>
                        <page string="Fahrzeug">
                            <group>
                                <group>
                                    <field name="car_manufacturer_id"/>
                                    <field name="car_type"/>
                                    <field name="license_plate"/>
                                    <field name="is_season_registration"/>
                                    <field name="season_start_date" invisible="is_season_registration != 'yes'"/>
                                    <field name="season_end_date" invisible="is_season_registration != 'yes'"/>
                                    <field name="first_registration_date"/>
                                    <field name="owner_registration_date"/>
                                    <field name="current_mileage"/>
                                    <field name="vehicle_value" widget="monetary"/>
                                    <field name="has_vat_deduction"/>
                                    <field name="payment_type"/>
                                </group>
                                <group>
                                    <!-- Removed duplicate fields that are already in other sections -->
                                    <!-- Driver fields moved to "Fahrer" page -->
                                    <!-- Usage and risk fields moved to "Nutzung & Risiko" page -->
                                    <field name="is_vehicle_at_owner_address"/>
                                </group>
                            </group>
                            <group invisible="is_vehicle_at_owner_address != 'no'">
                                <group string="Fahrzeugstandort">
                                    <field name="vehicle_street"/>
                                    <field name="vehicle_street_number"/>
                                    <field name="vehicle_street_addition"/>
                                    <field name="vehicle_zip"/>
                                    <field name="vehicle_city"/>
                                    <field name="vehicle_country_id"/>
                                </group>
                            </group>
                            <!-- Removed duplicate risk_requirement field that is already in "Nutzung & Risiko" page -->
                        </page>
                        <page string="Fahrer">
                            <group>
                                <group>
                                    <field name="driver1_name"/>
                                    <field name="driver1_birthdate"/>
                                    <field name="has_additional_driver"/>
                                </group>
                            </group>
                            <group invisible="has_additional_driver != 'yes'">
                                <group string="Fahrer 2">
                                    <field name="driver2_name"/>
                                    <field name="driver2_birthdate"/>
                                    <field name="has_driver3"/>
                                </group>
                            </group>
                            <group invisible="has_additional_driver != 'yes' or has_driver3 != 'yes'">
                                <group string="Fahrer 3">
                                    <field name="driver3_name"/>
                                    <field name="driver3_birthdate"/>
                                    <field name="has_driver4"/>
                                </group>
                            </group>
                            <group invisible="has_additional_driver != 'yes' or has_driver3 != 'yes' or has_driver4 != 'yes'">
                                <group string="Fahrer 4">
                                    <field name="driver4_name"/>
                                    <field name="driver4_birthdate"/>
                                </group>
                            </group>
                        </page>
                        <page string="Nutzung &amp; Risiko">
                            <group>
                                <group>
                                    <field name="usage_purpose"/>
                                    <field name="annual_mileage"/>
                                    <field name="has_tracker"/>
                                    <field name="has_previous_insurance"/>
                                    <field name="previous_insurance_details" invisible="has_previous_insurance != 'yes'"/>
                                    <field name="has_previous_damage"/>
                                    <field name="previous_damage_details" invisible="has_previous_damage != 'yes'"/>
                                    <field name="has_similar_vehicles"/>
                                    <field name="similar_vehicles_description" invisible="has_similar_vehicles != 'yes'"/>
                                    <field name="has_previous_experience"/>
                                </group>
                                <group>
                                    <field name="daily_vehicle_license"/>
                                    <field name="is_vehicle_at_owner_address"/>
                                    <field name="risk_requirement"/>
                                    <field name="has_burglar_alarm"/>
                                    <field name="has_alarm_police_connection" invisible="has_burglar_alarm != 'yes'"/>
                                    <field name="has_fire_alarm"/>
                                    <field name="has_fire_department_connection" invisible="has_fire_alarm != 'yes'"/>
                                </group>
                            </group>
                        </page>
                        <page string="Dokumente &amp; Bemerkungen">
                            <group>
                                <group>
                                    <field name="value_proof" filename="value_proof_filename"/>
                                    <field name="value_proof_filename" invisible="1"/>
                                    <field name="vehicle_documents" filename="vehicle_documents_filename"/>
                                    <field name="vehicle_documents_filename" invisible="1"/>
                                    <field name="photos" filename="photos_filename"/>
                                    <field name="photos_filename" invisible="1"/>
                                </group>
                                <group>
                                    <field name="notes"/>
                                    <field name="internal_notes"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_premiumcar_untervermittler_tree" model="ir.ui.view">
        <field name="name">premiumcar.untervermittler.tree</field>
        <field name="model">premiumcar.untervermittler</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="broker_first_name"/>
                <field name="broker_last_name"/>
                <field name="first_name"/>
                <field name="last_name"/>
                <field name="car_manufacturer_id"/>
                <field name="car_type"/>
                <field name="insurance_start_date"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="state"/>
                <field name="create_date"/>
            </tree>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_premiumcar_untervermittler_search" model="ir.ui.view">
        <field name="name">premiumcar.untervermittler.search</field>
        <field name="model">premiumcar.untervermittler</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="broker_first_name"/>
                <field name="broker_last_name"/>
                <field name="first_name"/>
                <field name="last_name"/>
                <field name="car_manufacturer_id"/>
                <field name="car_type"/>
                <separator/>
                <filter string="Entwurf" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Eingereicht" name="submitted" domain="[('state', '=', 'submitted')]"/>
                <filter string="Bearbeitet" name="processed" domain="[('state', '=', 'processed')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="status" context="{'group_by': 'state'}"/>
                    <filter string="Datum" name="create_date" context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_premiumcar_untervermittler" model="ir.actions.act_window">
        <field name="name">PremiumCar Erstanfrage Untervermittler</field>
        <field name="res_model">premiumcar.untervermittler</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Es wurden noch keine PremiumCar Erstanfrage Untervermittler erstellt
            </p>
        </field>
    </record>

    <!-- Action for PremiumCar Erstanfrage Untervermittler - Intern -->
    <record id="action_premiumcar_untervermittler_intern" model="ir.actions.act_window">
        <field name="name">PremiumCar Erstanfrage Untervermittler - Intern</field>
        <field name="res_model">premiumcar.untervermittler</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Es wurden noch keine PremiumCar Erstanfrage Untervermittler - Intern erstellt
            </p>
        </field>
    </record>

    <!-- Menu Item for PremiumCar Erstanfrage Untervermittler - Intern -->
    <menuitem id="menu_premiumcar_untervermittler_intern"
              parent="menu_premium_car_request_root"
              name="PremiumCar Erstanfrage Untervermittler - Intern"
              action="action_premiumcar_untervermittler_intern"
              sequence="15"/>

    <!-- Website Menu -->
    <record id="menu_website_premiumcar_untervermittler" model="website.menu">
        <field name="name">PremiumCar Erstanfrage Untervermittler - Intern</field>
        <field name="url">/premiumcar-erstanfrage-untervermittler-intern</field>
        <field name="parent_id" ref="website.main_menu"/>
        <field name="sequence" type="int">55</field>
        <field name="is_visible">True</field>
    </record>
</odoo>