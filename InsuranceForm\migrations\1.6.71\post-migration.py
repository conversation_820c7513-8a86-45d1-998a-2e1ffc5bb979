import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)

def migrate(cr, version):
    """
    Migration script to fix company-based access control issues.
    Ensures all records have proper company_id and record rules work correctly.
    """
    _logger.info("Starting migration v1.6.71 - Fixing company-based access control")

    env = api.Environment(cr, SUPERUSER_ID, {})

    # List of models to migrate
    models_to_migrate = [
        'premium.car.request',
        'premiumcar.untervermittler',
        'premiumcar.endkunden',
        'premiumcar.erstanfrage.endkunden.intern',
        'augencheck.request'
    ]

    # Get default company (first company in the system)
    default_company = env['res.company'].search([], limit=1)
    if not default_company:
        _logger.error("No companies found in the system!")
        return

    _logger.info(f"Using default company: {default_company.name} (ID: {default_company.id})")

    for model_name in models_to_migrate:
        try:
            _logger.info(f"Processing {model_name} records...")

            # Check if model exists
            if model_name not in env:
                _logger.warning(f"Model {model_name} not found, skipping...")
                continue

            model = env[model_name]

            # Find ALL records and check their company_id
            all_records = model.search([])
            _logger.info(f"Found {len(all_records)} total {model_name} records")

            records_without_company = model.search([
                ('company_id', '=', False)
            ])
            _logger.info(f"Found {len(records_without_company)} {model_name} records without company_id")

            updated_count = 0
            from_lead_count = 0
            default_company_count = 0

            for record in records_without_company:
                company_id = None

                # Try to get company from related CRM lead's policy provider
                if hasattr(record, 'lead_id') and record.lead_id:
                    if hasattr(record.lead_id, 'policy_provider_id') and record.lead_id.policy_provider_id:
                        company_id = record.lead_id.policy_provider_id.id
                        from_lead_count += 1
                        _logger.debug(f"Setting company_id to {record.lead_id.policy_provider_id.name} for {model_name} record {record.id}")

                # If no company found from lead, use default company
                if not company_id:
                    company_id = default_company.id
                    default_company_count += 1
                    _logger.debug(f"Setting default company_id to {default_company.name} for {model_name} record {record.id}")

                # Update the record
                if company_id:
                    try:
                        record.write({'company_id': company_id})
                        updated_count += 1
                    except Exception as e:
                        _logger.error(f"Error updating {model_name} record {record.id}: {e}")

            _logger.info(f"Updated {updated_count} {model_name} records")
            _logger.info(f"Set from CRM lead: {from_lead_count} records")
            _logger.info(f"Set to default company: {default_company_count} records")

            # Final verification
            remaining_without_company = model.search_count([('company_id', '=', False)])
            if remaining_without_company > 0:
                _logger.warning(f"Still {remaining_without_company} {model_name} records without company_id!")
            else:
                _logger.info(f"All {model_name} records now have company_id set")

        except Exception as e:
            _logger.error(f"Error migrating {model_name}: {e}")
            continue

    # Clear any cached record rules to ensure they are reloaded
    try:
        _logger.info("Clearing record rule cache...")
        env.registry.clear_cache()
        _logger.info("Record rule cache cleared")
    except Exception as e:
        _logger.warning(f"Could not clear cache: {e}")

    _logger.info("Migration v1.6.71 completed - company-based access control should now work properly")
