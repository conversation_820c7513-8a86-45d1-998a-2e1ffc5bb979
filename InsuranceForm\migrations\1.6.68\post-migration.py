import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)

def migrate(cr, version):
    """
    Migration script to fix company_id for existing insurance records
    and ensure proper company-based access control.
    """
    _logger.info("Starting migration 1.6.68 to fix company-based access control")
    
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    # List of models to migrate
    models_to_migrate = [
        'premium.car.request',
        'premiumcar.untervermittler', 
        'premiumcar.endkunden',
        'premiumcar.erstanfrage.endkunden.intern',
        'augencheck.request'
    ]
    
    # Get default company for fallback
    default_company = env['res.company'].search([], limit=1)
    if not default_company:
        _logger.error("No company found in the system! Cannot proceed with migration.")
        return
    
    _logger.info(f"Using default company: {default_company.name} (ID: {default_company.id})")
    
    for model_name in models_to_migrate:
        try:
            _logger.info(f"Migrating {model_name} records...")
            
            # Check if model exists
            if model_name not in env:
                _logger.warning(f"Model {model_name} not found, skipping...")
                continue
                
            model = env[model_name]
            
            # Find records without company_id set or with invalid company_id
            records_without_company = model.search([
                '|',
                ('company_id', '=', False),
                ('company_id', '=', None)
            ])
            
            _logger.info(f"Found {len(records_without_company)} {model_name} records without company_id")
            
            updated_count = 0
            lead_company_count = 0
            default_company_count = 0
            
            for record in records_without_company:
                company_id = None
                
                # Try to get company from related CRM lead's policy provider
                if hasattr(record, 'lead_id') and record.lead_id:
                    try:
                        if hasattr(record.lead_id, 'policy_provider_id') and record.lead_id.policy_provider_id:
                            company_id = record.lead_id.policy_provider_id.id
                            lead_company_count += 1
                            _logger.debug(f"Setting company_id to {record.lead_id.policy_provider_id.name} for {model_name} record {record.id}")
                    except Exception as e:
                        _logger.warning(f"Error getting policy provider from lead for {model_name} record {record.id}: {e}")
                
                # If no company found from lead, use default company
                if not company_id:
                    company_id = default_company.id
                    default_company_count += 1
                    _logger.debug(f"Setting default company_id to {default_company.name} for {model_name} record {record.id}")
                
                # Update the record
                if company_id:
                    try:
                        record.write({'company_id': company_id})
                        updated_count += 1
                    except Exception as e:
                        _logger.error(f"Error updating {model_name} record {record.id}: {e}")
                        
            _logger.info(f"Updated {updated_count} {model_name} records")
            _logger.info(f"Set from CRM lead policy provider: {lead_company_count} records")
            _logger.info(f"Set to default company: {default_company_count} records")
            
        except Exception as e:
            _logger.error(f"Error migrating {model_name}: {e}")
            continue
    
    # Also check for records with invalid company references
    _logger.info("Checking for records with invalid company references...")
    
    for model_name in models_to_migrate:
        try:
            if model_name not in env:
                continue
                
            model = env[model_name]
            
            # Get all company IDs that exist
            existing_company_ids = env['res.company'].search([]).ids
            
            # Find records with company_id that doesn't exist
            invalid_company_records = model.search([
                ('company_id', 'not in', existing_company_ids),
                ('company_id', '!=', False)
            ])
            
            if invalid_company_records:
                _logger.warning(f"Found {len(invalid_company_records)} {model_name} records with invalid company references")
                
                for record in invalid_company_records:
                    try:
                        record.write({'company_id': default_company.id})
                        _logger.info(f"Fixed invalid company reference for {model_name} record {record.id}")
                    except Exception as e:
                        _logger.error(f"Error fixing invalid company reference for {model_name} record {record.id}: {e}")
            
        except Exception as e:
            _logger.error(f"Error checking invalid company references for {model_name}: {e}")
            continue
    
    _logger.info("Migration 1.6.68 completed successfully - company-based access control should now work properly")
