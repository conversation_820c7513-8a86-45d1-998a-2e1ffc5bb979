import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)

def migrate(cr, version):
    """
    Migration script to force clear cached action definitions
    that contain problematic company_id domain filters.
    """
    _logger.info("Starting migration to clear cached action definitions (v1.6.78)")

    env = api.Environment(cr, SUPERUSER_ID, {})

    # List of action XML IDs that need to be cleared/reloaded
    action_xml_ids = [
        'InsuranceForm.action_premium_car_request',
        'InsuranceForm.action_premiumcar_untervermittler', 
        'InsuranceForm.action_premiumcar_endkunden',
        'InsuranceForm.action_premiumcar_erstanfrage_endkunden_intern',
        'InsuranceForm.action_augencheck_request'
    ]

    try:
        # Force reload of action definitions
        for xml_id in action_xml_ids:
            try:
                # Get the action record
                action_ref = env.ref(xml_id, raise_if_not_found=False)
                if action_ref:
                    _logger.info(f"Clearing cache for action: {xml_id}")
                    
                    # Force reload by touching the record
                    action_ref.write({'name': action_ref.name})
                    
                    # Clear any domain that might be cached
                    if hasattr(action_ref, 'domain') and action_ref.domain:
                        if 'company_id' in str(action_ref.domain):
                            _logger.warning(f"Found problematic domain in {xml_id}: {action_ref.domain}")
                            action_ref.write({'domain': False})
                            _logger.info(f"Cleared domain for {xml_id}")
                    
                else:
                    _logger.warning(f"Action not found: {xml_id}")
                    
            except Exception as e:
                _logger.error(f"Error processing action {xml_id}: {e}")
                continue

        # Clear model cache
        try:
            _logger.info("Clearing model cache...")
            env.registry.clear_cache()
            _logger.info("Model cache cleared successfully")
        except Exception as e:
            _logger.error(f"Error clearing model cache: {e}")

        # Clear action cache specifically
        try:
            _logger.info("Clearing action cache...")
            env['ir.actions.act_window'].clear_caches()
            _logger.info("Action cache cleared successfully")
        except Exception as e:
            _logger.error(f"Error clearing action cache: {e}")

        _logger.info("Migration completed successfully - cached actions should be cleared")

    except Exception as e:
        _logger.error(f"Migration failed: {e}")
        raise
