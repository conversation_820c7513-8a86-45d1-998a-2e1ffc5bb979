from odoo import models, fields, api, _
from odoo.exceptions import UserError
from datetime import date
import io
import base64
import traceback
import logging

_logger = logging.getLogger(__name__)

# Check if ReportLab is available
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, BaseDocTemplate, PageTemplate, Frame, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.units import inch
    from reportlab.lib.enums import TA_CENTER
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    _logger.warning("ReportLab is not available. PDF generation will not work.")

class PremiumcarErstanfrageEndkundenIntern(models.Model):
    _name = 'premiumcar.erstanfrage.endkunden.intern'
    _description = 'Premium Car Erstanfrage Endkunden - Intern'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Reference', readonly=True, copy=False, default='New')

    # Link to CRM Lead and Contact
    lead_id = fields.Many2one('crm.lead', string='Related Lead', ondelete='set null')
    partner_id = fields.Many2one('res.partner', string='Related Contact', ondelete='set null')

    # Company field for multi-company access control
    company_id = fields.Many2one('res.company', string='Company',
                                default=lambda self: self.env.company,
                                required=True,
                                help="Company that this request belongs to")

    # SQL constraints
    _sql_constraints = [
        ('company_id_required', 'CHECK (company_id IS NOT NULL)',
         'Company is required for all insurance requests.'),
    ]

    state = fields.Selection([
        ('draft', 'Entwurf'),
        ('submitted', 'Eingereicht'),
        ('processed', 'Bearbeitet')
    ], default='draft', string='Status', tracking=True)

    # Insurance Start Date
    insurance_start_date = fields.Date(string='Versicherungsbeginn', required=True, tracking=True)

    # Policyholder Information
    salutation = fields.Selection([
        ('mr', 'Herr'),
        ('mrs', 'Frau'),
        ('diverse', 'Divers'),
        ('company', 'Firma'),
        ('other', 'Sonstige')
    ], string='Anrede', required=True, tracking=True)
    first_name = fields.Char(string='Vorname', required=True, tracking=True)
    last_name = fields.Char(string='Nachname', required=True, tracking=True)
    street = fields.Char(string='Straße', required=True, tracking=True)
    street_number = fields.Char(string='Nr.', required=True, tracking=True)
    street_addition = fields.Char(string='Adresszusatz', tracking=True)
    zip = fields.Char(string='PLZ', required=True, tracking=True)
    city = fields.Char(string='Ort', required=True, tracking=True)
    country_id = fields.Many2one('res.country', string='Land', required=True, tracking=True)
    email = fields.Char(string='E-Mail', tracking=True)
    phone = fields.Char(string='Telefon', tracking=True)
    mobile = fields.Char(string='Mobiltelefon', tracking=True)
    is_owner_different = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Halter abweichend vom Versicherungsnehmer', required=True, tracking=True)

    # Owner Information (if different from policyholder)
    owner_salutation = fields.Selection([
        ('mr', 'Herr'),
        ('mrs', 'Frau'),
        ('diverse', 'Divers'),
        ('company', 'Firma'),
        ('other', 'Sonstige')
    ], string='Anrede (Halter)', tracking=True)
    owner_first_name = fields.Char(string='Vorname (Halter)', tracking=True)
    owner_last_name = fields.Char(string='Nachname (Halter)', tracking=True)
    owner_street = fields.Char(string='Straße (Halter)', tracking=True)
    owner_street_number = fields.Char(string='Nr. (Halter)', tracking=True)
    owner_street_addition = fields.Char(string='Adresszusatz (Halter)', tracking=True)
    owner_zip = fields.Char(string='PLZ (Halter)', tracking=True)
    owner_city = fields.Char(string='Ort (Halter)', tracking=True)
    owner_country_id = fields.Many2one('res.country', string='Land (Halter)', tracking=True)
    owner_email = fields.Char(string='E-Mail (Halter)', tracking=True)
    owner_phone = fields.Char(string='Telefon (Halter)', tracking=True)
    owner_mobile = fields.Char(string='Mobiltelefon (Halter)', tracking=True)

    # Vehicle Information
    car_manufacturer_id = fields.Many2one('vehicle.manufacturer', string='Fahrzeughersteller', required=True, tracking=True)
    car_type = fields.Char(string='Fahrzeugtyp', required=True, tracking=True)
    license_plate = fields.Char(string='Amtliches Kennzeichen', tracking=True)
    is_season_registration = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Wird das Fahrzeug auf Saison zugelassen', required=True, tracking=True)
    season_start_date = fields.Date(string='Saisonzeitraum Start', tracking=True)
    season_end_date = fields.Date(string='Saisonzeitraum Ende', tracking=True)
    first_registration_date = fields.Date(string='Erstzulassungsdatum', required=True, tracking=True)
    owner_registration_date = fields.Date(string='Zulassungsdatum auf den Versicherungsnehmer', required=True, tracking=True)
    current_mileage = fields.Integer(string='Aktueller km-Stand', required=True, tracking=True)
    vehicle_value = fields.Float(string='Fahrzeugwert Brutto', required=True, tracking=True)
    has_vat_deduction = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besteht Vorsteuerabzugsberechtigung', required=True, tracking=True)
    payment_type = fields.Selection([
        ('single', 'Einmalzahlung'),
        ('quarterly', 'Vierteljährlich'),
        ('biannual', 'Halbjährlich')
    ], string='Art der Bezahlung', required=True, tracking=True)

    # Driver Information
    driver1_name = fields.Char(string='Name Fahrer 1', required=True, tracking=True)
    driver1_birthdate = fields.Date(string='Geburtsdatum Fahrer 1', required=True, tracking=True)
    has_additional_driver = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Gibt es einen weiteren regelmäßigen Fahrer', required=True, tracking=True)

    # Additional Driver Information
    driver2_name = fields.Char(string='Name Fahrer 2', tracking=True)
    driver2_birthdate = fields.Date(string='Geburtsdatum Fahrer 2', tracking=True)
    has_driver3 = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Weiterer Fahrer 3', tracking=True)
    driver3_name = fields.Char(string='Name Fahrer 3', tracking=True)
    driver3_birthdate = fields.Date(string='Geburtsdatum Fahrer 3', tracking=True)
    has_driver4 = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Weiterer Fahrer 4', tracking=True)
    driver4_name = fields.Char(string='Name Fahrer 4', tracking=True)
    driver4_birthdate = fields.Date(string='Geburtsdatum Fahrer 4', tracking=True)

    # Usage & Risk
    usage_purpose = fields.Selection([
        ('private', 'Privat'),
        ('business', 'Geschäftlich'),
        ('mixed', 'Gemischt')
    ], string='Nutzungszweck', required=True, tracking=True)
    annual_mileage = fields.Integer(string='Jährliche Fahrleistung', required=True, tracking=True)
    has_tracker = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Ist ein Tracker eingebaut', required=True, tracking=True)
    has_previous_insurance = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Vorversicherung vorhanden?', tracking=True)
    previous_insurance_details = fields.Text(string='Versicherer und Versicherungsscheinnummer der Vorversicherung', tracking=True)
    has_previous_damage = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Gab es Vorschäden', required=True, tracking=True)
    liability_damage_count = fields.Integer(string='Anzahl Vorschäden Haftpflicht', tracking=True)
    full_coverage_damage_count = fields.Integer(string='Anzahl Vorschäden Vollkasko', tracking=True)
    partial_coverage_damage_count = fields.Integer(string='Anzahl Vorschäden Teilkasko', tracking=True)
    has_similar_vehicles = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Werden weitere gleichartige Fahrzeuge genutzt', required=True, tracking=True)
    similar_vehicles_description = fields.Text(string='Beschreibung der vergleichbaren Fahrzeuge', tracking=True)
    has_previous_experience = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besteht Erfahrung mit ähnlichen Fahrzeugen', required=True, tracking=True)
    daily_vehicle_license = fields.Selection([
        ('driver', 'Fahrer 1'),
        ('owner', 'Halter'),
        ('both', 'Beide')
    ], string='Täglicher Fahrzeugnachweis liegt vor für', required=True, tracking=True)
    license_plate_text = fields.Char(string='Kennzeichen des Alltagsfahrzeuges', tracking=True)
    is_vehicle_at_owner_address = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Fahrzeug steht dauerhaft an der Anschrift des Halters', required=True, tracking=True)

    # Additional fields for vergleichbare Fahrzeuge section
    vf_usage_purpose = fields.Selection([
        ('mostly_private', 'überwiegend privat'),
        ('mostly_business', 'überwiegend beruflich'),
        ('exclusively_private', 'ausschließlich privat'),
        ('exclusively_business', 'ausschließlich beruflich')
    ], string='Verwendungszweck des Fahrzeuges', tracking=True)
    vf_annual_mileage = fields.Integer(string='Jährliche Fahrleistung (Vergleichbare Fahrzeuge)', tracking=True)
    vf_has_tracker = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Tracker vorhanden?', tracking=True)
    vf_has_previous_insurance = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Vorversicherung vorhanden? (Vergleichbare Fahrzeuge)', tracking=True)
    vf_previous_insurance_details = fields.Text(string='Versicherer und Versicherungsscheinnummer der Vorversicherung (Vergleichbare Fahrzeuge)', tracking=True)
    vf_has_previous_damage = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Sind Vorschäden im Bereich KFZ vorhanden?', tracking=True)
    vf_liability_damage_count = fields.Integer(string='Anzahl Vorschäden Haftpflicht (Vergleichbare Fahrzeuge)', tracking=True)
    vf_full_coverage_damage_count = fields.Integer(string='Anzahl Vorschäden Vollkasko (Vergleichbare Fahrzeuge)', tracking=True)
    vf_partial_coverage_damage_count = fields.Integer(string='Anzahl Vorschäden Teilkasko (Vergleichbare Fahrzeuge)', tracking=True)

    # Vehicle Location (if different from owner address)
    vehicle_street = fields.Char(string='Straße (Fahrzeugstandort)', tracking=True)
    vehicle_street_number = fields.Char(string='Nr. (Fahrzeugstandort)', tracking=True)
    vehicle_street_addition = fields.Char(string='Adresszusatz (Fahrzeugstandort)', tracking=True)
    vehicle_zip = fields.Char(string='PLZ (Fahrzeugstandort)', tracking=True)
    vehicle_city = fields.Char(string='Ort (Fahrzeugstandort)', tracking=True)
    vehicle_country_id = fields.Many2one('res.country', string='Land (Fahrzeugstandort)', tracking=True)

    risk_requirement = fields.Selection([
        ('single_garage', 'Einzel-/Doppelgarage'),
        ('communal_garage', 'Sammelgarage/Halle'),
        ('no_garage', 'keine abschließbare, überdachte und durch feste Wände umschlossene Abstellmöglichkeit')
    ], string='Risikoannahmevoraussetzung für das Fahrzeug', required=True, tracking=True)
    has_burglar_alarm = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Einbruchmeldeanlage vorhanden', required=True, tracking=True)
    has_alarm_police_connection = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besteht eine Aufschaltung (z.B. zur Polizei)', tracking=True)
    has_fire_alarm = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Brandmeldeanlage vorhanden', required=True, tracking=True)

    has_fire_department_connection = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besteht eine Aufschaltung zur Feuerwehr', tracking=True)

    # Documents & Notes
    value_proof = fields.Binary(string='Wertnachweis')
    value_proof_filename = fields.Char(string='Wertnachweis Dateiname')
    vehicle_documents = fields.Binary(string='Fahrzeugunterlagen')
    vehicle_documents_filename = fields.Char(string='Fahrzeugunterlagen Dateiname')
    photos = fields.Binary(string='Fotos', attachment=True)
    photos_filename = fields.Char(string='Fotos Dateiname')
    notes = fields.Text(string='Bemerkungen', tracking=True)
    notizen = fields.Text(string='Notizen', tracking=True)
    internal_notes = fields.Text(string='Interne Bemerkungen', tracking=True)

    def action_view_lead(self):
        """Open the CRM lead linked to this record."""
        self.ensure_one()
        if not self.lead_id:
            return

        return {
            'name': 'CRM Lead',
            'view_mode': 'form',
            'res_model': 'crm.lead',
            'res_id': self.lead_id.id,
            'type': 'ir.actions.act_window',
        }

    @api.model
    def create(self, vals):
        import logging
        _logger = logging.getLogger(__name__)

        # Create a savepoint for transaction management
        cr = self.env.cr
        cr.execute('SAVEPOINT premiumcar_erstanfrage_endkunden_intern_create')

        try:
            # Generate a timestamp-based reference number like in premiumcar_endkunden
            if vals.get('name', 'New') == 'New':
                # Get the sequence prefix
                seq_prefix = self.env['ir.sequence'].next_by_code('premiumcar.erstanfrage.endkunden.intern') or 'PCEI-'
                # Generate timestamp part
                timestamp = fields.Datetime.now().strftime('%Y%m%d%H%M%S')
                # Combine to create reference number
                vals['name'] = f"{seq_prefix}{timestamp}"

            # Create a contact record for this customer
            try:
                # Use the same reference number as the form record for consistency
                reference_code = vals.get('name', 'New')
                if reference_code == 'New':
                    # If name is not set yet, generate a reference code
                    timestamp = fields.Datetime.now().strftime('%Y%m%d%H%M%S')
                    reference_code = f"PCEI-{timestamp}"

                # Use clean name without timestamp
                clean_name = f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip()

                # Include reference code in the name for better searchability
                partner_name = f"{clean_name} ({reference_code})"

                partner_vals = {
                    'name': partner_name,
                    'ref': reference_code,  # Store the reference code in the standard reference field
                    'street': vals.get('street', ''),
                    'street2': vals.get('street_addition', ''),
                    'zip': vals.get('zip', ''),
                    'city': vals.get('city', ''),
                    'country_id': vals.get('country_id'),
                    'email': vals.get('email', ''),
                    'phone': vals.get('phone', ''),
                    'mobile': vals.get('mobile', ''),
                    'type': 'contact',
                    'company_type': 'person',
                    'comment': f"Created from PremiumCar Erstanfrage Endkunden Intern form {reference_code}",
                    'function': 'Fahrzeugversicherung',  # Add job position
                }

                # Log the reference code for debugging
                import logging
                _logger = logging.getLogger(__name__)
                _logger.info(f"Setting contact reference code to: {reference_code}")

                # Set the proper title based on salutation
                if vals.get('salutation') == 'mr':
                    title = self.env.ref('base.res_partner_title_mister', False)
                    if title:
                        partner_vals['title'] = title.id
                elif vals.get('salutation') == 'mrs':
                    title = self.env.ref('base.res_partner_title_madam', False)
                    if title:
                        partner_vals['title'] = title.id

                # Check for existing partner to avoid duplicates
                existing_partner = False

                # Check by email if provided
                if partner_vals.get('email'):
                    existing_partner = self.env['res.partner'].sudo().search([
                        ('email', '=', partner_vals.get('email'))
                    ], limit=1)
                    if existing_partner:
                        _logger.info(f"Found existing partner with email {partner_vals.get('email')}: ID {existing_partner.id}")

                # Check by phone if no match by email
                if not existing_partner and partner_vals.get('phone'):
                    existing_partner = self.env['res.partner'].sudo().search([
                        ('phone', '=', partner_vals.get('phone'))
                    ], limit=1)
                    if existing_partner:
                        _logger.info(f"Found existing partner with phone {partner_vals.get('phone')}: ID {existing_partner.id}")

                if existing_partner:
                    # Use existing partner instead of creating a new one
                    vals['partner_id'] = existing_partner.id
                    _logger.info(f"Using existing partner (ID: {existing_partner.id}) instead of creating a new one")

                    # Update the reference number if it's not set or if it doesn't start with PCEI-
                    if not existing_partner.ref or not existing_partner.ref.startswith('PCEI-'):
                        try:
                            # Update both ref and name to include the reference code
                            update_vals = {'ref': reference_code}

                            # Only update name if it doesn't already contain the reference code
                            if reference_code not in existing_partner.name:
                                update_vals['name'] = f"{existing_partner.name} ({reference_code})"

                            existing_partner.sudo().write(update_vals)
                            _logger.info(f"Updated existing partner reference to: {reference_code}")
                        except Exception as ref_e:
                            _logger.error(f"Error updating partner reference: {str(ref_e)}")
                            # Continue without updating the reference
                else:
                    # Define mail context to disable auto-followers
                    MailCtx = {
                        'mail_create_nolog': True,
                        'mail_notrack': True,
                        'tracking_disable': True,
                        'mail_auto_subscription': False,
                        'disable_duplicate_check': True,
                    }

                    # Create the partner with a savepoint to ensure transaction integrity
                    with self.env.cr.savepoint():
                        partner = self.env['res.partner'].sudo().with_context(**MailCtx).create(partner_vals)
                        vals['partner_id'] = partner.id
                        _logger.info(f"Successfully created contact with ID {partner.id} from PremiumCar Erstanfrage Endkunden Intern form")
            except Exception as e:
                _logger.error(f"Error creating contact: {str(e)}")
                # Continue without creating the contact
                _logger.info("Continuing without creating a contact")

            # Continue with record creation
            record = super(PremiumcarErstanfrageEndkundenIntern, self).create(vals)

            # Create a savepoint before attempting CRM lead creation
            cr.execute('SAVEPOINT premiumcar_erstanfrage_lead_create')

            # Try to create a CRM lead using the opportunity mapper
            try:
                # Define mapping for Selection fields between form and CRM lead
                SELECTION_MAPS = {
                    'policy_holder_gender': {
                        'male': 'male',
                        'female': 'female',
                        'diverse': 'others',
                        'company': 'others',
                        'other': 'others',
                    },
                    'usage_purpose': {
                        'private': 'only_private',
                        'business': 'only_professional',
                        'mixed': 'mainly_private',
                        'mostly_private': 'only_private',
                        'mostly_business': 'only_professional',
                        'exclusively_private': 'only_private',
                        'exclusively_business': 'only_professional',
                        'only_private': 'only_private',
                        'only_business': 'only_professional',
                    },
                    'risk_requirement': {
                        'single_garage': 'singe_or_double_garage',
                        'communal_garage': 'collective_garage_or_hall',
                        'no_garage': 'no_lockable_parking_space',
                    },
                    'payment_type': {
                        'single': 'cash_payment',
                        'quarterly': 'financing',
                        'biannual': 'leasing',
                        'cash': 'cash_payment',
                        'financing': 'financing',
                        'leasing': 'leasing',
                    },
                    'burglary_alarm_system': {
                        True: 'burglary_alarm_system',
                        False: 'no_burglary_alarm_system',
                        'yes': 'burglary_alarm_system',
                        'no': 'no_burglary_alarm_system',
                    },
                    'fire_alarm_system': {
                        True: 'fire_alarm_system',
                        False: 'no_fire_alarm_system',
                        'yes': 'fire_alarm_system',
                        'no': 'no_fire_alarm_system',
                    },
                }

                # Helper function to translate selection values
                def translate_selection(field_name, value):
                    if not value or field_name not in SELECTION_MAPS:
                        return value
                    return SELECTION_MAPS[field_name].get(value, value)

                # Map salutation to gender
                gender_value = None
                if vals.get('salutation') == 'mr':
                    gender_value = 'male'
                elif vals.get('salutation') == 'mrs':
                    gender_value = 'female'
                elif vals.get('salutation') == 'diverse':
                    gender_value = 'others'
                elif vals.get('salutation') == 'company':
                    gender_value = 'others'
                else:
                    gender_value = 'others'

                # Prepare data for opportunity mapper with all required fields
                form_data = {
                    # Contact information
                    'first_name': vals.get('first_name'),
                    'last_name': vals.get('last_name'),
                    'email': vals.get('email'),
                    'phone': vals.get('phone'),
                    'mobile': vals.get('mobile'),
                    'salutation': vals.get('salutation'),

                    # Address information
                    'street': vals.get('street'),
                    'street_number': vals.get('street_number'),
                    'street_addition': vals.get('street_addition'),
                    'zip': vals.get('zip'),
                    'city': vals.get('city'),
                    'country_id': vals.get('country_id'),

                    # Vehicle information
                    'car_manufacturer_id': vals.get('car_manufacturer_id'),
                    'car_type': vals.get('car_type'),
                    'license_plate': vals.get('license_plate'),
                    'first_registration_date': vals.get('first_registration_date'),
                    'owner_registration_date': vals.get('owner_registration_date'),
                    'vehicle_value': vals.get('vehicle_value'),
                    'current_mileage': vals.get('current_mileage'),
                    'annual_mileage': vals.get('annual_mileage'),

                    # Insurance information
                    'insurance_start_date': vals.get('insurance_start_date'),
                    'is_owner_different': vals.get('is_owner_different'),
                    'is_season_registration': vals.get('is_season_registration'),
                    'season_start_date': vals.get('season_start_date'),
                    'season_end_date': vals.get('season_end_date'),
                    'payment_type': vals.get('payment_type'),
                    'has_vat_deduction': vals.get('has_vat_deduction'),
                    'usage_purpose': vals.get('usage_purpose'),

                    # Driver information
                    'driver1_name': vals.get('driver1_name'),
                    'driver1_birthdate': vals.get('driver1_birthdate'),
                    'has_additional_driver': vals.get('has_additional_driver'),
                    'driver2_name': vals.get('driver2_name'),
                    'driver2_birthdate': vals.get('driver2_birthdate'),
                    'has_driver3': vals.get('has_driver3'),
                    'driver3_name': vals.get('driver3_name'),
                    'driver3_birthdate': vals.get('driver3_birthdate'),
                    'has_driver4': vals.get('has_driver4'),
                    'driver4_name': vals.get('driver4_name'),
                    'driver4_birthdate': vals.get('driver4_birthdate'),

                    # Risk information
                    'has_tracker': vals.get('has_tracker'),
                    'has_previous_insurance': vals.get('has_previous_insurance'),
                    'previous_insurance_details': vals.get('previous_insurance_details'),
                    'has_previous_damage': vals.get('has_previous_damage'),
                    'liability_damage_count': vals.get('liability_damage_count'),
                    'full_coverage_damage_count': vals.get('full_coverage_damage_count'),
                    'partial_coverage_damage_count': vals.get('partial_coverage_damage_count'),
                    'has_similar_vehicles': vals.get('has_similar_vehicles'),
                    'similar_vehicles_description': vals.get('similar_vehicles_description'),
                    'has_previous_experience': vals.get('has_previous_experience'),
                    'daily_vehicle_license': vals.get('daily_vehicle_license'),
                    'license_plate_text': vals.get('license_plate_text'),
                    'is_vehicle_at_owner_address': vals.get('is_vehicle_at_owner_address'),
                    'risk_requirement': vals.get('risk_requirement'),
                    'has_burglar_alarm': vals.get('has_burglar_alarm'),
                    'has_alarm_police_connection': vals.get('has_alarm_police_connection'),
                    'has_fire_alarm': vals.get('has_fire_alarm'),
                    'has_fire_department_connection': vals.get('has_fire_department_connection'),
                    'notes': vals.get('notes'),

                    # Additional notes
                    'notizen': vals.get('notizen'),
                    'internal_notes': vals.get('internal_notes'),

                    # Required fields for CRM lead mapping with direct mapping
                    'policy_holder_dob': vals.get('driver1_birthdate'),
                    'policy_holder_gender': gender_value,

                    # Map boolean fields with proper conversion
                    'divergent_vehicle_owner': vals.get('is_owner_different') == 'yes',
                    'seasonal_registration': vals.get('is_season_registration') == 'yes',
                    'tracker': vals.get('has_tracker') == 'yes',
                    'existing_insurance': vals.get('has_previous_insurance') == 'yes',
                    'previous_claims': vals.get('has_previous_damage') == 'yes',
                    'owning_comparable_vehicles': vals.get('has_similar_vehicles') == 'yes',
                    'driving_experience_with_comparable_vehicle': vals.get('has_previous_experience') == 'yes',
                    'main_vehicle_location_at_policyholder_address': vals.get('is_vehicle_at_owner_address') == 'yes',

                    # Map to CRM fields with different names
                    'vehicle_manufacturer': vals.get('car_manufacturer_id'),
                    'vehicle_type': vals.get('car_type'),
                    'first_registration': vals.get('first_registration_date'),
                    'registration_on_policyholder': vals.get('owner_registration_date'),
                    'current_milage': vals.get('current_mileage'),  # Note the spelling difference
                    'annual_milage': vals.get('annual_mileage'),    # Note the spelling difference
                    'payment_type_for_vehicle': vals.get('payment_type'),
                    'license_plate_of_daily_use_vehicle': vals.get('license_plate_text'),
                    'risk_acceptance_condition': vals.get('risk_requirement'),
                    'comparable_vehicles': vals.get('similar_vehicles_description'),
                    'other_information': vals.get('notes'),

                    # Set policy category to 'vehicle' for Premium Car
                    'policy_category': 'vehicle',
                    'category': 'vehicle',

                    # Map alarm systems
                    'burglary_alarm_system': 'burglary_alarm_system' if vals.get('has_burglar_alarm') == 'yes' else 'no_burglary_alarm_system',
                    'fire_alarm_system': 'fire_alarm_system' if vals.get('has_fire_alarm') == 'yes' else 'no_fire_alarm_system',

                    # Map driver information to CRM fields
                    'driver_1': vals.get('driver1_name'),
                    'dob_driver_1': vals.get('driver1_birthdate'),
                    'driver_2': vals.get('driver2_name'),
                    'dob_driver_2': vals.get('driver2_birthdate'),
                    'driver_3': vals.get('driver3_name'),
                    'dob_driver_3': vals.get('driver3_birthdate'),
                    'driver_4': vals.get('driver4_name'),
                    'dob_driver_4': vals.get('driver4_birthdate'),

                    # Map owner information to policy holder fields
                    'policy_holder_name': f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip(),
                    'policy_holder_street': vals.get('street'),
                    'policy_holder_street_2': vals.get('street_addition'),
                    'policy_holder_zip': vals.get('zip'),
                    'policy_holder_city': vals.get('city'),
                    'policy_holder_country': vals.get('country_id'),
                    'policy_holder_email': vals.get('email'),
                    'policy_holder_phone': vals.get('phone'),
                    'policy_holder_mobile': vals.get('mobile'),

                    # Map vehicle location if different from owner address
                    'main_vehicle_location': 'different_address' if vals.get('is_vehicle_at_owner_address') == 'no' else 'policyholder_address',
                    'risk_address_street': vals.get('vehicle_street'),
                    'risk_address_street_2': vals.get('vehicle_street_addition'),
                    'risk_address_zip': vals.get('vehicle_zip'),
                    'risk_address_city': vals.get('vehicle_city'),
                    'risk_address_country': vals.get('vehicle_country_id'),
                }

                # Try to use opportunity mapper if it exists
                mapper_exists = False
                try:
                    mapper_exists = self.env['opportunity.mapper'] and True
                    _logger.info("opportunity.mapper model is available")
                except Exception as e:
                    _logger.warning(f"opportunity.mapper model is not available: {e}")

                if mapper_exists:
                    try:
                        lead_vals = self.env['opportunity.mapper'].map_form_to_opportunity(form_data, 'premiumcar_erstanfrage_endkunden_intern')

                        # Apply selection field translations
                        for field_name in SELECTION_MAPS:
                            if field_name in lead_vals:
                                lead_vals[field_name] = translate_selection(field_name, lead_vals[field_name])

                        # Get insurance category for vehicle insurance if not already set
                        if 'insurance_category_id' not in lead_vals or not lead_vals['insurance_category_id']:
                            try:
                                vehicle_insurance_category = self.env['insurance.category'].sudo().search(
                                    [('name', '=', 'Vehicle Insurance')], limit=1)
                                if not vehicle_insurance_category:
                                    # Try with different naming patterns
                                    vehicle_insurance_category = self.env['insurance.category'].sudo().search(
                                        ['|', ('name', 'ilike', 'vehicle'), ('name', 'ilike', 'fahrzeug')], limit=1)

                                if vehicle_insurance_category:
                                    lead_vals['insurance_category_id'] = vehicle_insurance_category.id
                                    _logger.info(f"Set insurance_category_id to {vehicle_insurance_category.id} ({vehicle_insurance_category.name})")
                            except Exception as e:
                                _logger.error(f"Error finding vehicle insurance category: {str(e)}")

                        # Ensure policy_holder_dob is set
                        if 'policy_holder_dob' not in lead_vals or not lead_vals['policy_holder_dob']:
                            lead_vals['policy_holder_dob'] = vals.get('driver1_birthdate')
                            _logger.info(f"Set policy_holder_dob to {vals.get('driver1_birthdate')}")

                        # Ensure policy_holder_gender is set
                        if 'policy_holder_gender' not in lead_vals or not lead_vals['policy_holder_gender']:
                            gender_value = 'male'
                            if vals.get('salutation') == 'mrs':
                                gender_value = 'female'
                            elif vals.get('salutation') in ['diverse', 'company', 'other']:
                                gender_value = 'others'
                            lead_vals['policy_holder_gender'] = gender_value
                            _logger.info(f"Set policy_holder_gender to {gender_value}")

                        # Link to partner if created
                        if vals.get('partner_id'):
                            lead_vals['partner_id'] = vals.get('partner_id')
                            _logger.debug(f"Linked lead to partner ID {vals.get('partner_id')}")

                        # Define mail context to disable auto-followers
                        MailCtx = {
                            'mail_create_nolog': True,
                            'mail_notrack': True,
                            'tracking_disable': True,
                            'mail_auto_subscription': False,
                        }

                        # Always remove message_follower_ids to avoid duplicates
                        lead_vals.pop('message_follower_ids', None)

                        # Add reference code to website_ref_number field
                        if reference_code:
                            lead_vals['website_ref_number'] = reference_code
                            # Also add to description for backward compatibility
                            if lead_vals.get('description'):
                                lead_vals['description'] = f"Reference Number: {reference_code}\n\n" + lead_vals['description']
                            else:
                                lead_vals['description'] = f"Reference Number: {reference_code}"
                            _logger.info(f"Added reference code {reference_code} to website_ref_number field")

                        # Log the lead values for debugging
                        _logger.info(f"Creating CRM lead with the following values:")
                        for key, value in lead_vals.items():
                            _logger.info(f"  {key}: {value}")

                        # Create the lead
                        lead = self.env['crm.lead'].sudo().with_context(**MailCtx).create(lead_vals)
                        _logger.info(f"Successfully created CRM lead with ID {lead.id} from premiumcar erstanfrage endkunden intern form")

                        # Set company_id from the created lead's policy provider
                        if lead.policy_provider_id:
                            record.write({'company_id': lead.policy_provider_id.id})
                            _logger.info(f"Set company_id to {lead.policy_provider_id.name} from CRM lead")

                        # Subscribe partner manually (only once)
                        if vals.get('partner_id'):
                            lead.message_subscribe(partner_ids=[vals['partner_id']])
                            _logger.info(f"Manually subscribed partner {vals['partner_id']} to lead {lead.id}")

                        # Link the lead to the record
                        record.write({'lead_id': lead.id})
                    except Exception as e:
                        _logger.error(f"Error creating lead via mapper: {str(e)}")

                        # Get more detailed error information
                        import traceback
                        _logger.error(f"Traceback: {traceback.format_exc()}")

                        # Check for specific errors
                        error_str = str(e)
                        if "policy_holder_dob" in error_str:
                            _logger.error("Error related to policy_holder_dob field. Make sure driver1_birthdate is a valid date.")
                        elif "policy_holder_gender" in error_str:
                            _logger.error("Error related to policy_holder_gender field. Make sure salutation is properly mapped.")
                        elif "insurance_category_id" in error_str:
                            _logger.error("Error related to insurance_category_id field. Make sure vehicle insurance category exists.")

                        # Rollback to savepoint
                        cr.execute('ROLLBACK TO SAVEPOINT premiumcar_erstanfrage_lead_create')
                        # Continue without creating the lead
                else:
                    # Basic lead creation if mapper doesn't exist
                    try:
                        full_name = f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip()

                        # Get insurance category for vehicle insurance
                        vehicle_insurance_category = False
                        try:
                            vehicle_insurance_category = self.env['insurance.category'].sudo().search(
                                [('name', '=', 'Vehicle Insurance')], limit=1)
                            if not vehicle_insurance_category:
                                # Try with different naming patterns
                                vehicle_insurance_category = self.env['insurance.category'].sudo().search(
                                    ['|', ('name', 'ilike', 'vehicle'), ('name', 'ilike', 'fahrzeug')], limit=1)
                            _logger.info(f"Found vehicle insurance category: {vehicle_insurance_category.name if vehicle_insurance_category else 'None'}")
                        except Exception as e:
                            _logger.error(f"Error finding vehicle insurance category: {str(e)}")

                        # Map gender from salutation
                        gender_value = 'male'
                        if vals.get('salutation') == 'mrs':
                            gender_value = 'female'
                        elif vals.get('salutation') in ['diverse', 'company', 'other']:
                            gender_value = 'others'

                        # Get CRM fields to check if fields exist
                        try:
                            crm_fields = self.env['crm.lead'].sudo().fields_get()
                            _logger.info(f"Retrieved {len(crm_fields)} fields from crm.lead model")
                        except Exception as e:
                            _logger.error(f"Error getting crm.lead fields: {str(e)}")
                            crm_fields = {}

                        # Create comprehensive lead values with all required fields
                        lead_vals = {
                            'name': f"Premium Car Erstanfrage von {full_name}",
                            'type': 'lead',
                            'contact_name': full_name,
                            'email_from': vals.get('email', ''),
                            'phone': vals.get('phone', ''),
                            'mobile': vals.get('mobile', ''),
                            'description': f"Premium Car Erstanfrage von {full_name}",

                            # Add required fields from CRM lead model
                            'policy_holder_dob': vals.get('driver1_birthdate'),
                            'policy_holder_gender': gender_value,
                            'insurance_category_id': vehicle_insurance_category.id if vehicle_insurance_category else False,

                            # Add address fields
                            'street': vals.get('street', ''),
                            'street2': vals.get('street_addition', ''),
                            'city': vals.get('city', ''),
                            'zip': vals.get('zip', ''),
                            'function': 'Fahrzeugversicherung',

                            # Add vehicle information
                            'vehicle_type': vals.get('car_type', ''),
                            'vehicle_value': vals.get('vehicle_value', 0),
                            'license_plate': vals.get('license_plate', ''),
                            'first_registration': vals.get('first_registration_date'),
                            'registration_on_policyholder': vals.get('owner_registration_date'),
                            'annual_milage': vals.get('annual_mileage', 0),
                            'current_milage': vals.get('current_mileage', 0),
                            'insurance_start_date': vals.get('insurance_start_date'),

                            # Add boolean fields with proper conversion
                            'divergent_vehicle_owner': vals.get('is_owner_different') == 'yes',
                            'seasonal_registration': vals.get('is_season_registration') == 'yes',
                            'tracker': vals.get('has_tracker') == 'yes',
                            'existing_insurance': vals.get('has_previous_insurance') == 'yes',
                            'previous_claims': vals.get('has_previous_damage') == 'yes',
                            'owning_comparable_vehicles': vals.get('has_similar_vehicles') == 'yes',
                            'driving_experience_with_comparable_vehicle': vals.get('has_previous_experience') == 'yes',
                            'main_vehicle_location_at_policyholder_address': vals.get('is_vehicle_at_owner_address') == 'yes',

                            # Add driver information
                            'driver_1': vals.get('driver1_name'),
                            'dob_driver_1': vals.get('driver1_birthdate'),
                            'driver_2': vals.get('driver2_name'),
                            'dob_driver_2': vals.get('driver2_birthdate'),
                            'driver_3': vals.get('driver3_name'),
                            'dob_driver_3': vals.get('driver3_birthdate'),
                            'driver_4': vals.get('driver4_name'),
                            'dob_driver_4': vals.get('driver4_birthdate'),

                            # Add policy holder information
                            'policy_holder_name': full_name,
                            'policy_holder_street': vals.get('street'),
                            'policy_holder_street_2': vals.get('street_addition'),
                            'policy_holder_zip': vals.get('zip'),
                            'policy_holder_city': vals.get('city'),
                            'policy_holder_email': vals.get('email'),
                            'policy_holder_phone': vals.get('phone'),
                            'policy_holder_mobile': vals.get('mobile'),

                            # Add risk information
                            'risk_acceptance_condition': SELECTION_MAPS['risk_requirement'].get(
                                vals.get('risk_requirement'), 'singe_or_double_garage'),
                            'burglary_alarm_system': SELECTION_MAPS['burglary_alarm_system'].get(
                                vals.get('has_burglar_alarm') == 'yes', 'no_burglary_alarm_system'),
                            'fire_alarm_system': SELECTION_MAPS['fire_alarm_system'].get(
                                vals.get('has_fire_alarm') == 'yes', 'no_fire_alarm_system'),

                            # Add damage counts
                            'number_of_claim_in_motor_liability_insurance': vals.get('liability_damage_count', 0),
                            'number_of_claims_in_comprehensive_insurance': vals.get('full_coverage_damage_count', 0),
                            'number_of_claims_in_partial_coverage_insurance': vals.get('partial_coverage_damage_count', 0),

                            # Add vehicle location information
                            'main_vehicle_location': 'different_address' if vals.get('is_vehicle_at_owner_address') == 'no' else 'policyholder_address',
                            'risk_address_street': vals.get('vehicle_street'),
                            'risk_address_street_2': vals.get('vehicle_street_addition'),
                            'risk_address_zip': vals.get('vehicle_zip'),
                            'risk_address_city': vals.get('vehicle_city'),

                            # Add usage purpose
                            'usage_purpose': SELECTION_MAPS['usage_purpose'].get(vals.get('usage_purpose'), 'only_private'),

                            # Add payment type
                            'payment_type_for_vehicle': SELECTION_MAPS['payment_type'].get(vals.get('payment_type'), 'cash_payment'),

                            # Add additional information
                            'other_information': vals.get('notes'),
                            'previous_insurer_and_policy_number': vals.get('previous_insurance_details'),
                            'comparable_vehicles': vals.get('similar_vehicles_description'),
                            'license_plate_of_daily_use_vehicle': vals.get('license_plate_text'),
                        }

                        # Filter out fields that don't exist in the CRM lead model
                        filtered_lead_vals = {}
                        for field, value in lead_vals.items():
                            if field in crm_fields:
                                filtered_lead_vals[field] = value
                            else:
                                _logger.info(f"Skipping field '{field}' as it doesn't exist in crm.lead model")

                        # Make sure required fields are always included
                        for required_field in ['policy_holder_dob', 'policy_holder_gender', 'insurance_category_id']:
                            if required_field not in filtered_lead_vals and required_field in lead_vals:
                                filtered_lead_vals[required_field] = lead_vals[required_field]
                                _logger.info(f"Adding required field '{required_field}' even though it wasn't found in crm.lead fields")

                        lead_vals = filtered_lead_vals

                        # Link to partner if created
                        if vals.get('partner_id'):
                            lead_vals['partner_id'] = vals.get('partner_id')
                            _logger.debug(f"Linked lead to partner ID {vals.get('partner_id')}")

                        # Add reference code to website_ref_number field
                        if reference_code:
                            lead_vals['website_ref_number'] = reference_code
                            # Also add to description for backward compatibility
                            if lead_vals.get('description'):
                                lead_vals['description'] = f"Reference Number: {reference_code}\n\n" + lead_vals['description']
                            else:
                                lead_vals['description'] = f"Reference Number: {reference_code}"
                            _logger.info(f"Added reference code {reference_code} to website_ref_number field")

                        # Define mail context to disable auto-followers
                        MailCtx = {
                            'mail_create_nolog': True,
                            'mail_notrack': True,
                            'tracking_disable': True,
                            'mail_auto_subscription': False,
                        }

                        # Log the lead values for debugging
                        _logger.info(f"Creating basic CRM lead with the following values:")
                        for key, value in lead_vals.items():
                            _logger.info(f"  {key}: {value}")

                        # Create the lead
                        lead = self.env['crm.lead'].sudo().with_context(**MailCtx).create(lead_vals)
                        _logger.info(f"Successfully created basic CRM lead with ID {lead.id} from premiumcar erstanfrage endkunden intern form")

                        # Set company_id from the created lead's policy provider
                        if lead.policy_provider_id:
                            record.write({'company_id': lead.policy_provider_id.id})
                            _logger.info(f"Set company_id to {lead.policy_provider_id.name} from CRM lead")

                        # Subscribe partner manually (only once)
                        if vals.get('partner_id'):
                            lead.message_subscribe(partner_ids=[vals['partner_id']])
                            _logger.info(f"Manually subscribed partner {vals['partner_id']} to lead {lead.id}")

                        # Link the lead to the record
                        record.write({'lead_id': lead.id})
                    except Exception as e:
                        _logger.error(f"Error creating basic lead: {str(e)}")

                        # Get more detailed error information
                        import traceback
                        _logger.error(f"Traceback: {traceback.format_exc()}")

                        # Check for specific errors
                        error_str = str(e)
                        if "policy_holder_dob" in error_str:
                            _logger.error("Error related to policy_holder_dob field. Make sure driver1_birthdate is a valid date.")
                        elif "policy_holder_gender" in error_str:
                            _logger.error("Error related to policy_holder_gender field. Make sure salutation is properly mapped.")
                        elif "insurance_category_id" in error_str:
                            _logger.error("Error related to insurance_category_id field. Make sure vehicle insurance category exists.")

                        # Rollback to savepoint
                        cr.execute('ROLLBACK TO SAVEPOINT premiumcar_erstanfrage_lead_create')
                        # Continue without creating the lead
            except Exception as e:
                _logger.error(f"Error in CRM lead creation block: {str(e)}")
                # Rollback to savepoint
                cr.execute('ROLLBACK TO SAVEPOINT premiumcar_erstanfrage_lead_create')
                # Continue without creating the lead

            return record
        except Exception as e:
            _logger.error(f"Error creating premiumcar erstanfrage endkunden intern record: {str(e)}")
            # Rollback to main savepoint
            cr.execute('ROLLBACK TO SAVEPOINT premiumcar_erstanfrage_endkunden_intern_create')
            # Re-raise the exception
            raise

    def action_view_lead(self):
        """Open the CRM lead form view"""
        self.ensure_one()
        if not self.lead_id:
            return

        return {
            'name': 'CRM Lead',
            'type': 'ir.actions.act_window',
            'res_model': 'crm.lead',
            'res_id': self.lead_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def generate_pdf_summary(self):
        """Generate a PDF summary of the Premium Car Erstanfrage Endkunden Intern request"""
        self.ensure_one()

        if not REPORTLAB_AVAILABLE:
            _logger.error("ReportLab is not available. Cannot generate PDF.")
            raise UserError(_("PDF generation is not available. Please contact your administrator."))

        try:
            # Create a buffer to hold the PDF
            buffer = io.BytesIO()

            # Create the PDF document with custom page template for logo support
            doc = BaseDocTemplate(buffer, pagesize=A4, leftMargin=0.75*inch, rightMargin=0.75*inch)

            # Create a frame for the content
            frame = Frame(0.75*inch, 0.75*inch, A4[0] - 1.5*inch, A4[1] - 1.5*inch,
                         leftPadding=0, bottomPadding=0, rightPadding=0, topPadding=0)

            # Create page template with header/footer function
            page_template = PageTemplate(id='main', frames=[frame], onPage=self._create_header_footer)
            doc.addPageTemplates([page_template])

            story = []

            # Get styles
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                textColor=colors.darkred,
                alignment=1  # Center alignment
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                textColor=colors.darkblue
            )

            normal_style = styles['Normal']

            # Create a style for table labels to enable text wrapping
            label_style = ParagraphStyle(
                'LabelStyle',
                parent=styles['Normal'],
                fontSize=10,
                fontName='Helvetica-Bold',
                leading=12
            )

            value_style = ParagraphStyle(
                'ValueStyle',
                parent=styles['Normal'],
                fontSize=10,
                fontName='Helvetica',
                leading=12
            )

            # Add large spacer to push title below logo and improve page breaks
            story.append(Spacer(1, 80))

            # Title
            story.append(Paragraph("PremiumCar Erstanfrage Endkunden-Intern - Zusammenfassung", title_style))
            story.append(Spacer(1, 20))

            # Reference number
            story.append(Paragraph(f"<b>Referenznummer:</b> {self.name}", normal_style))
            story.append(Spacer(1, 20))

            # Personal Information Section
            story.append(Paragraph("Persönliche Daten", heading_style))

            personal_data = [
                [Paragraph('Anrede:', label_style), Paragraph(self._get_salutation_display(), value_style)],
                [Paragraph('Vorname:', label_style), Paragraph(self.first_name or '', value_style)],
                [Paragraph('Nachname:', label_style), Paragraph(self.last_name or '', value_style)],
                [Paragraph('Versicherungsbeginn:', label_style), Paragraph(self.insurance_start_date.strftime('%d.%m.%Y') if self.insurance_start_date else '', value_style)],
            ]

            personal_table = Table(personal_data, colWidths=[3.2*inch, 3.3*inch])
            personal_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(personal_table)
            story.append(Spacer(1, 20))

            # Continue with more sections in the next part...
            return self._continue_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

        except Exception as e:
            _logger.error(f"Error generating PDF: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            raise UserError(_("Error generating PDF. Please try again or contact support."))

    def _continue_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Continue PDF generation with remaining sections"""

        # Address Information Section
        story.append(Paragraph("Adresse", heading_style))

        address_data = [
            [Paragraph('Straße:', label_style), Paragraph(f"{self.street or ''} {self.street_number or ''}".strip(), value_style)],
            [Paragraph('Adresszusatz:', label_style), Paragraph(self.street_addition or '', value_style)],
            [Paragraph('PLZ:', label_style), Paragraph(self.zip or '', value_style)],
            [Paragraph('Ort:', label_style), Paragraph(self.city or '', value_style)],
            [Paragraph('Land:', label_style), Paragraph(self.country_id.name if self.country_id else '', value_style)],
        ]

        address_table = Table(address_data, colWidths=[3.2*inch, 3.3*inch])
        address_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(address_table)
        story.append(Spacer(1, 20))

        # Contact Information Section
        story.append(Paragraph("Kontaktdaten", heading_style))

        contact_data = [
            [Paragraph('E-Mail:', label_style), Paragraph(self.email or '', value_style)],
            [Paragraph('Telefonnummer:', label_style), Paragraph(self._format_phone_number(self.phone), value_style)],
            [Paragraph('Mobilfunknummer:', label_style), Paragraph(self._format_phone_number(self.mobile), value_style)],
        ]

        contact_table = Table(contact_data, colWidths=[3.2*inch, 3.3*inch])
        contact_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(contact_table)
        story.append(Spacer(1, 20))

        # Owner Information Section (only if different from policyholder)
        if self.is_owner_different == 'yes':
            story.append(Paragraph("Halter-Informationen", heading_style))

            owner_data = [
                [Paragraph('Anrede (Halter):', label_style), Paragraph(self._get_owner_salutation_display(), value_style)],
                [Paragraph('Vorname (Halter):', label_style), Paragraph(self.owner_first_name or '', value_style)],
                [Paragraph('Nachname (Halter):', label_style), Paragraph(self.owner_last_name or '', value_style)],
                [Paragraph('Straße (Halter):', label_style), Paragraph(f"{self.owner_street or ''} {self.owner_street_number or ''}".strip(), value_style)],
                [Paragraph('Adresszusatz (Halter):', label_style), Paragraph(self.owner_street_addition or '', value_style)],
                [Paragraph('PLZ (Halter):', label_style), Paragraph(self.owner_zip or '', value_style)],
                [Paragraph('Ort (Halter):', label_style), Paragraph(self.owner_city or '', value_style)],
                [Paragraph('Land (Halter):', label_style), Paragraph(self.owner_country_id.name if self.owner_country_id else '', value_style)],
                [Paragraph('E-Mail (Halter):', label_style), Paragraph(self.owner_email or '', value_style)],
                [Paragraph('Telefonnummer (Halter):', label_style), Paragraph(self._format_phone_number(self.owner_phone), value_style)],
                [Paragraph('Mobilfunknummer (Halter):', label_style), Paragraph(self._format_phone_number(self.owner_mobile), value_style)],
            ]

            owner_table = Table(owner_data, colWidths=[3.2*inch, 3.3*inch])
            owner_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(owner_table)
            story.append(Spacer(1, 20))

        # Continue with vehicle information...
        return self._continue_vehicle_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

    def _continue_vehicle_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Continue PDF generation with vehicle information sections"""

        # Vehicle Information Section
        story.append(Paragraph("Fahrzeuginformationen", heading_style))

        vehicle_data = [
            [Paragraph('Fahrzeughersteller:', label_style), Paragraph(self.car_manufacturer_id.name if self.car_manufacturer_id else '', value_style)],
            [Paragraph('Fahrzeugtyp:', label_style), Paragraph(self.car_type or '', value_style)],
            [Paragraph('Amtliches Kennzeichen:', label_style), Paragraph(self.license_plate or '', value_style)],
        ]

        vehicle_table = Table(vehicle_data, colWidths=[3.2*inch, 3.3*inch])
        vehicle_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(vehicle_table)
        story.append(Spacer(1, 20))

        # Registration Information Section
        story.append(Paragraph("Zulassungsinformationen", heading_style))

        registration_data = [
            [Paragraph('Saison-Zulassung:', label_style), Paragraph(self._get_is_season_registration_display(), value_style)],
            [Paragraph('Erstzulassungsdatum:', label_style), Paragraph(self.first_registration_date.strftime('%d.%m.%Y') if self.first_registration_date else '', value_style)],
            [Paragraph('Zulassungsdatum auf Versicherungsnehmer:', label_style), Paragraph(self.owner_registration_date.strftime('%d.%m.%Y') if self.owner_registration_date else '', value_style)],
        ]

        # Add seasonal period if applicable
        if self.is_season_registration == 'yes':
            if self.season_start_date:
                registration_data.append([Paragraph('Saisonzeitraum von:', label_style), Paragraph(self.season_start_date.strftime('%d.%m.%Y'), value_style)])
            if self.season_end_date:
                registration_data.append([Paragraph('Saisonzeitraum bis:', label_style), Paragraph(self.season_end_date.strftime('%d.%m.%Y'), value_style)])

        registration_table = Table(registration_data, colWidths=[3.2*inch, 3.3*inch])
        registration_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(registration_table)
        story.append(Spacer(1, 20))

        # Vehicle Value & Details Section
        story.append(Paragraph("Fahrzeugwert und Details", heading_style))

        value_data = [
            [Paragraph('Aktueller km-Stand:', label_style), Paragraph(f"{self.current_mileage:,}" if self.current_mileage else '', value_style)],
            [Paragraph('Fahrzeugwert Brutto:', label_style), Paragraph(f"{self.vehicle_value:,.2f} €" if self.vehicle_value else '', value_style)],
            [Paragraph('Vorsteuerabzugsberechtigung:', label_style), Paragraph(self._get_has_vat_deduction_display(), value_style)],
            [Paragraph('Zahlart des Fahrzeuges:', label_style), Paragraph(self._get_payment_type_display(), value_style)],
        ]

        value_table = Table(value_data, colWidths=[3.2*inch, 3.3*inch])
        value_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(value_table)
        story.append(Spacer(1, 20))

        # Continue with driver information...
        return self._continue_driver_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

    def _continue_driver_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Continue PDF generation with driver information sections"""

        # Driver Information Section
        story.append(Paragraph("Fahrerinformationen", heading_style))

        driver_data = [
            [Paragraph('Fahrer 1 Name:', label_style), Paragraph(self.driver1_name or '', value_style)],
            [Paragraph('Fahrer 1 Geburtsdatum:', label_style), Paragraph(self.driver1_birthdate.strftime('%d.%m.%Y') if self.driver1_birthdate else '', value_style)],
        ]

        # Add additional drivers if they exist
        if self.has_additional_driver == 'yes' and self.driver2_name:
            driver_data.extend([
                [Paragraph('Fahrer 2 Name:', label_style), Paragraph(self.driver2_name, value_style)],
                [Paragraph('Fahrer 2 Geburtsdatum:', label_style), Paragraph(self.driver2_birthdate.strftime('%d.%m.%Y') if self.driver2_birthdate else '', value_style)],
            ])

        if self.has_driver3 == 'yes' and self.driver3_name:
            driver_data.extend([
                [Paragraph('Fahrer 3 Name:', label_style), Paragraph(self.driver3_name, value_style)],
                [Paragraph('Fahrer 3 Geburtsdatum:', label_style), Paragraph(self.driver3_birthdate.strftime('%d.%m.%Y') if self.driver3_birthdate else '', value_style)],
            ])

        if self.has_driver4 == 'yes' and self.driver4_name:
            driver_data.extend([
                [Paragraph('Fahrer 4 Name:', label_style), Paragraph(self.driver4_name, value_style)],
                [Paragraph('Fahrer 4 Geburtsdatum:', label_style), Paragraph(self.driver4_birthdate.strftime('%d.%m.%Y') if self.driver4_birthdate else '', value_style)],
            ])

        driver_table = Table(driver_data, colWidths=[3.2*inch, 3.3*inch])
        driver_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(driver_table)
        story.append(Spacer(1, 20))

        # Vergleichbare Fahrzeuge Section (unique to intern form)
        story.append(Paragraph("Vergleichbare Fahrzeuge", heading_style))

        vf_data = [
            [Paragraph('Verwendungszweck des Fahrzeuges:', label_style), Paragraph(self._get_vf_usage_purpose_display(), value_style)],
            [Paragraph('Jährliche Fahrleistung:', label_style), Paragraph(f"{self.vf_annual_mileage:,} km" if self.vf_annual_mileage else '', value_style)],
            [Paragraph('Tracker vorhanden:', label_style), Paragraph(self._get_vf_has_tracker_display(), value_style)],
            [Paragraph('Vorversicherung vorhanden:', label_style), Paragraph(self._get_vf_has_previous_insurance_display(), value_style)],
        ]

        # Add previous insurance details if applicable
        if self.vf_has_previous_insurance == 'yes' and self.vf_previous_insurance_details:
            vf_data.append([Paragraph('Details der Vorversicherung:', label_style), Paragraph(self.vf_previous_insurance_details, value_style)])

        vf_data.append([Paragraph('Vorschäden vorhanden:', label_style), Paragraph(self._get_vf_has_previous_damage_display(), value_style)])

        # Add damage counts if applicable
        if self.vf_has_previous_damage == 'yes':
            if self.vf_liability_damage_count is not None and self.vf_liability_damage_count > 0:
                vf_data.append([Paragraph('Anzahl Vorschäden Haftpflicht:', label_style), Paragraph(str(self.vf_liability_damage_count), value_style)])
            if self.vf_full_coverage_damage_count is not None and self.vf_full_coverage_damage_count > 0:
                vf_data.append([Paragraph('Anzahl Vorschäden Vollkasko:', label_style), Paragraph(str(self.vf_full_coverage_damage_count), value_style)])
            if self.vf_partial_coverage_damage_count is not None and self.vf_partial_coverage_damage_count > 0:
                vf_data.append([Paragraph('Anzahl Vorschäden Teilkasko:', label_style), Paragraph(str(self.vf_partial_coverage_damage_count), value_style)])

        vf_data.extend([
            [Paragraph('Weitere vergleichbare Fahrzeuge:', label_style), Paragraph(self._get_has_similar_vehicles_display(), value_style)],
            [Paragraph('Vorerfahrung auf vergleichbaren Fahrzeugen:', label_style), Paragraph(self._get_has_previous_experience_display(), value_style)],
        ])

        # Add similar vehicles description if applicable
        if self.has_similar_vehicles == 'yes' and self.similar_vehicles_description:
            vf_data.append([Paragraph('Beschreibung vergleichbarer Fahrzeuge:', label_style), Paragraph(self.similar_vehicles_description, value_style)])

        # Add daily vehicle license if provided
        if self.license_plate_text:
            vf_data.append([Paragraph('Kennzeichen Alltagsfahrzeug:', label_style), Paragraph(self.license_plate_text, value_style)])

        vf_data.append([Paragraph('Fahrzeugstandort bei Versicherungsnehmer:', label_style), Paragraph(self._get_is_vehicle_at_owner_address_display(), value_style)])

        vf_table = Table(vf_data, colWidths=[3.2*inch, 3.3*inch])
        vf_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(vf_table)
        story.append(Spacer(1, 20))

        # Continue with final sections...
        return self._finalize_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

    def _finalize_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Finalize PDF generation with remaining sections"""

        # Vehicle Location Section (only if different from owner address)
        if self.is_vehicle_at_owner_address == 'no':
            story.append(Paragraph("Fahrzeugstandort", heading_style))

            location_data = [
                [Paragraph('Straße (Fahrzeugstandort):', label_style), Paragraph(f"{self.vehicle_street or ''} {self.vehicle_street_number or ''}".strip(), value_style)],
                [Paragraph('Adresszusatz (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_street_addition or '', value_style)],
                [Paragraph('PLZ (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_zip or '', value_style)],
                [Paragraph('Ort (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_city or '', value_style)],
                [Paragraph('Land (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_country_id.name if self.vehicle_country_id else '', value_style)],
            ]

            location_table = Table(location_data, colWidths=[3.2*inch, 3.3*inch])
            location_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(location_table)
            story.append(Spacer(1, 20))

        # Risk Information Section
        story.append(Paragraph("Risikoinformationen", heading_style))

        risk_data = [
            [Paragraph('Risikoannahmevoraussetzung:', label_style), Paragraph(self._get_risk_requirement_display(), value_style)],
            [Paragraph('Einbruchmeldeanlage vorhanden:', label_style), Paragraph(self._get_has_burglar_alarm_display(), value_style)],
            [Paragraph('Brandmeldeanlage vorhanden:', label_style), Paragraph(self._get_has_fire_alarm_display(), value_style)],
        ]

        # Add connection details if alarms are present
        if self.has_burglar_alarm == 'yes' and self.has_alarm_police_connection:
            risk_data.append([Paragraph('Aufschaltung Einbruchmeldeanlage:', label_style), Paragraph(self._get_has_alarm_police_connection_display(), value_style)])

        if self.has_fire_alarm == 'yes' and self.has_fire_department_connection:
            risk_data.append([Paragraph('Aufschaltung Brandmeldeanlage:', label_style), Paragraph(self._get_has_fire_department_connection_display(), value_style)])

        risk_table = Table(risk_data, colWidths=[3.2*inch, 3.3*inch])
        risk_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(risk_table)
        story.append(Spacer(1, 20))

        # Additional Notes Section (only if provided)
        notes_added = False
        if self.notes and self.notes.strip() and self.notes.strip().lower() not in ['no', 'nein', '']:
            if not notes_added:
                story.append(Paragraph("Weitere Mitteilungen", heading_style))
                notes_added = True

            notes_data = [[Paragraph('Bemerkungen:', label_style), Paragraph(self.notes, value_style)]]
            notes_table = Table(notes_data, colWidths=[3.2*inch, 3.3*inch])
            notes_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(notes_table)
            story.append(Spacer(1, 10))

        # Internal Notes Section (only if provided)
        if self.notizen and self.notizen.strip() and self.notizen.strip().lower() not in ['no', 'nein', '']:
            if not notes_added:
                story.append(Paragraph("Weitere Mitteilungen", heading_style))
                notes_added = True

            notizen_data = [[Paragraph('Notizen:', label_style), Paragraph(self.notizen, value_style)]]
            notizen_table = Table(notizen_data, colWidths=[3.2*inch, 3.3*inch])
            notizen_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(notizen_table)
            story.append(Spacer(1, 10))

        if notes_added:
            story.append(Spacer(1, 10))

        # Continue with final sections...
        return self._complete_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

    def _complete_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Complete PDF generation with uploaded documents and footer"""

        # Uploaded Documents Section (only if any documents were uploaded)
        uploaded_docs = []
        if self.value_proof and self.value_proof_filename:
            uploaded_docs.append(['Wertnachweis:', self.value_proof_filename])
        if self.vehicle_documents and self.vehicle_documents_filename:
            uploaded_docs.append(['Fahrzeugunterlagen:', self.vehicle_documents_filename])
        if self.photos and self.photos_filename:
            uploaded_docs.append(['Fotos:', self.photos_filename])

        if uploaded_docs:
            story.append(Paragraph("Hochgeladene Dokumente", heading_style))

            docs_data = [[Paragraph(label, label_style), Paragraph(filename, value_style)] for label, filename in uploaded_docs]
            docs_table = Table(docs_data, colWidths=[3.2*inch, 3.3*inch])
            docs_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(docs_table)
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_text = f"Erstellt am: {fields.Datetime.now().strftime('%d.%m.%Y %H:%M')} Uhr"
        footer_style = ParagraphStyle(
            'Footer',
            parent=normal_style,
            fontSize=8,
            textColor=colors.grey,
            alignment=TA_CENTER
        )
        story.append(Paragraph(footer_text, footer_style))

        # Build PDF
        doc.build(story)

        # Get the PDF data
        pdf_data = buffer.getvalue()
        buffer.close()

        # Return base64 encoded PDF
        return base64.b64encode(pdf_data)

    def _format_phone_number(self, phone_number):
        """Format phone number to ensure it displays with country code prefix"""
        if not phone_number:
            return ''

        # If the phone number doesn't start with +, add it
        # (since our controller stores them as international numbers)
        if phone_number and not phone_number.startswith('+'):
            return f'+{phone_number}'

        return phone_number

    def _get_logo_path(self):
        """Get the path to the logo file for PDF header"""
        import os

        try:
            _logger.info("=== GETTING LOGO PATH ===")

            # List of possible logo paths to try
            possible_paths = []

            # Method 1: Using __file__ to get current module directory
            try:
                current_dir = os.path.dirname(os.path.abspath(__file__))
                _logger.info(f"Current module directory: {current_dir}")

                # Go up to InsuranceForm directory, then to static/img
                insurance_form_dir = os.path.dirname(current_dir)  # Go up from models/ to InsuranceForm/
                possible_paths.append(os.path.join(insurance_form_dir, 'static', 'img', 'focus_logo.png'))

            except Exception as e:
                _logger.error(f"Error getting current directory: {e}")

            # Method 2: Using Odoo's get_module_path if available
            try:
                from odoo.modules import get_module_path
                module_path_odoo = get_module_path('InsuranceForm')
                if module_path_odoo:
                    possible_paths.append(os.path.join(module_path_odoo, 'static', 'img', 'focus_logo.png'))
            except Exception as e:
                _logger.error(f"Error getting module path via Odoo: {e}")

            # Method 3: Try relative paths from common Odoo locations
            try:
                import odoo
                odoo_dir = os.path.dirname(odoo.__file__)
                addons_paths = [
                    os.path.join(odoo_dir, '..', 'addons', 'InsuranceForm', 'static', 'img', 'focus_logo.png'),
                    os.path.join(odoo_dir, 'addons', 'InsuranceForm', 'static', 'img', 'focus_logo.png'),
                ]
                possible_paths.extend(addons_paths)
            except Exception as e:
                _logger.error(f"Error getting Odoo addons paths: {e}")

            _logger.info(f"Possible logo paths to check: {possible_paths}")

            # Check each path and return the first one that exists
            for path in possible_paths:
                abs_path = os.path.abspath(path)
                _logger.info(f"Checking logo path: {abs_path}")
                if os.path.exists(abs_path):
                    _logger.info(f"Found logo at: {abs_path}")
                    return abs_path
                else:
                    _logger.info(f"Logo not found at: {abs_path}")

            _logger.warning("Logo file not found in any of the expected locations")
            return None

        except Exception as e:
            _logger.error(f"Error getting logo path: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _create_header_footer(self, canvas, doc):
        """Create header with logo and footer for PDF pages"""
        import os
        try:
            _logger.info("=== HEADER FUNCTION CALLED ===")

            # Get logo path
            logo_path = self._get_logo_path()

            if logo_path and os.path.exists(logo_path):
                _logger.info(f"Attempting to draw logo from: {logo_path}")

                # Draw logo in top-right corner with professional sizing
                # Position: 0.4 inch from right edge, 0.25 inch from top (reverted to original position)
                # Title will be positioned below the logo for better visual hierarchy
                logo_width = 2.0 * inch   # Increased from 1.2 to 2.0 inches (67% larger)
                logo_height = 1.0 * inch  # Increased from 0.6 to 1.0 inches (67% larger)

                x_position = A4[0] - 0.4*inch - logo_width  # Right edge minus padding minus logo width
                y_position = A4[1] - 0.25*inch - logo_height  # Top edge minus padding minus logo height (reverted to original)

                _logger.info(f"Drawing logo at position: x={x_position}, y={y_position}, width={logo_width}, height={logo_height}")

                canvas.drawImage(logo_path, x_position, y_position,
                               width=logo_width, height=logo_height,
                               preserveAspectRatio=True, mask='auto')

                _logger.info("Logo successfully drawn to PDF")
            else:
                _logger.info("No valid logo file found, skipping logo in PDF header")

        except Exception as e:
            # Log error but don't fail PDF generation
            _logger.error(f"Error adding logo to PDF: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            pass

    # Helper methods for displaying selection field values
    def _get_salutation_display(self):
        """Get display value for salutation"""
        salutation_dict = dict(self._fields['salutation'].selection)
        return salutation_dict.get(self.salutation, '')

    def _get_owner_salutation_display(self):
        """Get display value for owner salutation"""
        salutation_dict = dict(self._fields['salutation'].selection)
        return salutation_dict.get(self.owner_salutation, '')

    def _get_is_season_registration_display(self):
        """Get display value for season registration"""
        season_dict = dict(self._fields['is_season_registration'].selection)
        return season_dict.get(self.is_season_registration, '')

    def _get_has_vat_deduction_display(self):
        """Get display value for VAT deduction"""
        vat_dict = dict(self._fields['has_vat_deduction'].selection)
        return vat_dict.get(self.has_vat_deduction, '')

    def _get_payment_type_display(self):
        """Get display value for payment type"""
        payment_dict = dict(self._fields['payment_type'].selection)
        return payment_dict.get(self.payment_type, '')

    def _get_vf_usage_purpose_display(self):
        """Get display value for VF usage purpose"""
        usage_dict = dict(self._fields['vf_usage_purpose'].selection)
        return usage_dict.get(self.vf_usage_purpose, '')

    def _get_vf_has_tracker_display(self):
        """Get display value for VF tracker"""
        tracker_dict = dict(self._fields['vf_has_tracker'].selection)
        return tracker_dict.get(self.vf_has_tracker, '')

    def _get_vf_has_previous_insurance_display(self):
        """Get display value for VF previous insurance"""
        insurance_dict = dict(self._fields['vf_has_previous_insurance'].selection)
        return insurance_dict.get(self.vf_has_previous_insurance, '')

    def _get_vf_has_previous_damage_display(self):
        """Get display value for VF previous damage"""
        damage_dict = dict(self._fields['vf_has_previous_damage'].selection)
        return damage_dict.get(self.vf_has_previous_damage, '')

    def _get_has_similar_vehicles_display(self):
        """Get display value for similar vehicles"""
        similar_dict = dict(self._fields['has_similar_vehicles'].selection)
        return similar_dict.get(self.has_similar_vehicles, '')

    def _get_has_previous_experience_display(self):
        """Get display value for previous experience"""
        experience_dict = dict(self._fields['has_previous_experience'].selection)
        return experience_dict.get(self.has_previous_experience, '')

    def _get_is_vehicle_at_owner_address_display(self):
        """Get display value for vehicle at owner address"""
        location_dict = dict(self._fields['is_vehicle_at_owner_address'].selection)
        return location_dict.get(self.is_vehicle_at_owner_address, '')

    def _get_risk_requirement_display(self):
        """Get display value for risk requirement"""
        risk_dict = dict(self._fields['risk_requirement'].selection)
        return risk_dict.get(self.risk_requirement, '')

    def _get_has_burglar_alarm_display(self):
        """Get display value for burglar alarm"""
        alarm_dict = dict(self._fields['has_burglar_alarm'].selection)
        return alarm_dict.get(self.has_burglar_alarm, '')

    def _get_has_fire_alarm_display(self):
        """Get display value for fire alarm"""
        fire_dict = dict(self._fields['has_fire_alarm'].selection)
        return fire_dict.get(self.has_fire_alarm, '')

    def _get_has_alarm_police_connection_display(self):
        """Get display value for alarm police connection"""
        connection_dict = dict(self._fields['has_alarm_police_connection'].selection)
        return connection_dict.get(self.has_alarm_police_connection, '')

    def _get_has_fire_department_connection_display(self):
        """Get display value for fire department connection"""
        fire_connection_dict = dict(self._fields['has_fire_department_connection'].selection)
        return fire_connection_dict.get(self.has_fire_department_connection, '')