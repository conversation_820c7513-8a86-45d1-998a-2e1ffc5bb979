<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <template id="premium_car_form_template" name="PremiumCar Erstanfrage Untervermittler">
        <t t-call="website.layout">
            <div class="container my-5">
                <div class="row">
                    <div class="col-12">
                        <!-- Debug information - hidden by default -->
                        <div class="alert alert-info mb-4 d-none">
                            <strong>Debug:</strong> Template premium_car_form_template loaded successfully
                        </div>

                        <h1 class="text-danger mb-4">Sehr geehrte Damen und Herren,</h1>
                        <p>bitte machen Sie die folgenden Angaben und klicken Sie abschließend auf "Absenden"</p>
                        <p>Viele Grüße<br/>Ihre Focus Team</p>

                        <form action="/premiumcar-erstanfrage-untervermittler/submit" method="post" class="mt-4">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>

                            <div class="mb-4">
                                <h4 class="text-danger">Daten des Vermittlers</h4>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="salutation">Anrede</label>
                                <select class="form-select" name="salutation" required="1">
                                    <option value="">-- Bitte wählen --</option>
                                    <option value="mr">Herr</option>
                                    <option value="mrs">Frau</option>
                                    <option value="diverse">Divers</option>
                                    <option value="company">Firma</option>
                                    <option value="other">Sonstige</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="first_name">Vorname</label>
                                <input type="text" class="form-control" name="first_name" required="1"/>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="last_name">Nachname</label>
                                <input type="text" class="form-control" name="last_name" required="1"/>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="birth_date">Geburtsdatum</label>
                                <input type="date" class="form-control" name="birth_date" required="1" placeholder="TT.MM.JJJJ"/>
                                <small class="text-muted">Bitte wählen Sie ein Datum aus.</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="street_number">Nr.</label>
                                <input type="text" class="form-control" name="street_number" required="1"/>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="street_addition">Andresszusatz</label>
                                <input type="text" class="form-control" name="street_addition" required="1"/>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="zip">PLZ</label>
                                <input type="text" class="form-control" name="zip" required="1"/>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="city">Ort</label>
                                <input type="text" class="form-control" name="city" required="1"/>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="country_id">Land</label>
                                <select class="form-select" name="country_id" required="1" id="country_select">
                                    <option value="">-- Bitte wählen --</option>
                                    <t t-foreach="countries" t-as="country">
                                        <option t-att-value="country.id" t-att-selected="country.code == 'DE' and 'selected'" t-att-data-code="country.phone_code or ''">
                                            <t t-esc="country.name"/>
                                        </option>
                                    </t>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="email">E-Mail</label>
                                <input type="email" class="form-control" name="email" placeholder="Ihre E-Mail-Adresse" required="1"/>
                            </div>

                            <div class="mb-3">
                                <label class="form-label" for="phone">Telefonnummer</label>
                                <div class="input-group">
                                    <select class="input-group-text" name="phone_code" id="phone_code_select">
                                        <t t-foreach="countries" t-as="country">
                                            <option t-att-value="country.phone_code" t-att-data-country-id="country.id" t-att-selected="country.code == 'DE' and 'selected'">
                                                <t t-esc="country.code"/> +<t t-esc="country.phone_code"/>
                                            </option>
                                        </t>
                                    </select>
                                    <input type="tel" class="form-control" name="phone" placeholder="Ihre Nummer (ohne Vorwahl)" required="1"/>
                                </div>
                                <small class="text-muted">Bitte nur die Nummer ohne Ländervorwahl eingeben</small>
                            </div>

                            <div class="mb-4">
                                <label class="form-label">Können Sie uns bestätigen, dass Ihnen eine aktuelle/unterschriebene Vollmacht des Kunden vorliegt?</label>
                                <small class="text-danger d-block">Pflichtangabe</small>
                                <div class="mt-2">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="has_customer_authority" id="authority_yes" value="yes" required="1"/>
                                        <label class="form-check-label" for="authority_yes">Ja</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="has_customer_authority" id="authority_no" value="no"/>
                                        <label class="form-check-label" for="authority_no">Nein</label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <button type="submit" class="btn btn-danger">Absenden</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <script type="text/javascript">
                $(document).ready(function() {
                    // Sync country dropdown with phone code dropdown
                    $('#country_select').on('change', function() {
                        var countryId = $(this).val();
                        if (countryId) {
                            $('#phone_code_select option[data-country-id="' + countryId + '"]').prop('selected', true);
                        }
                    });

                    // Sync phone code dropdown with country dropdown
                    $('#phone_code_select').on('change', function() {
                        var countryId = $(this).find('option:selected').data('country-id');
                        if (countryId) {
                            $('#country_select').val(countryId);
                        }
                    });
                });
            </script>
        </t>
    </template>

    <template id="premium_car_thank_you_template" name="PremiumCar Erstanfrage Untervermittler Thank You">
        <t t-call="website.layout">
            <div class="container my-5">
                <div class="row">
                    <div class="col-12 text-center">
                        <!-- Debug information - hidden by default -->
                        <div class="alert alert-info mb-4 d-none">
                            <strong>Debug:</strong> Template premium_car_thank_you_template loaded successfully
                        </div>

                        <h1 class="mt-5 mb-3">Vielen Dank für Ihre Anfrage!</h1>
                        <p class="lead mb-4">Wir werden uns in Kürze mit Ihnen in Verbindung setzen.</p>

                        <!-- Show CRM‑lead reference first -->
                        <t t-if="record and record.lead_id and record.lead_id.website_ref_number">
                            <div class="alert alert-info my-4">
                                <p><strong>Ihre Referenznummer:</strong>
                                   <span class="font-weight-bold" t-esc="record.lead_id.website_ref_number"/></p>
                                <p>Bitte bewahren Sie diese Nummer für Rückfragen auf.</p>
                            </div>
                        </t>

                        <!-- Fallback to partner reference if lead ref is missing -->
                        <t t-if="record and record.partner_id and record.partner_id.ref and not record.lead_id.website_ref_number">
                            <div class="alert alert-info my-4">
                                <p><strong>Ihre Referenznummer:</strong>
                                   <span class="font-weight-bold" t-esc="record.partner_id.ref"/></p>
                                <p>Bitte bewahren Sie diese Nummer für Rückfragen auf.</p>
                            </div>
                        </t>


                        <!-- PDF Download Section - Only show if available -->
                        <t t-if="record">
                            <t t-try="">
                                <t t-set="pdf_available" t-value="True"/>
                                <div class="card border-success my-4">
                                    <div class="card-header bg-light">
                                        <h5 class="card-title mb-0">
                                            <i class="fa fa-file-pdf-o text-danger me-2"></i>
                                            Ihre Anfrage als PDF herunterladen
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">
                                            Laden Sie eine Zusammenfassung Ihrer Anfrage als PDF herunter.
                                            Diese enthält alle von Ihnen eingegebenen Daten und Ihre Referenznummer.
                                        </p>
                                        <a t-attf-href="/premiumcar-erstanfrage-untervermittler/download-pdf/#{record.id}"
                                           class="btn btn-outline-danger">
                                            <i class="fa fa-download me-2"></i>
                                            PDF herunterladen
                                        </a>
                                    </div>
                                </div>
                                <t t-except="">
                                    <!-- PDF generation not available -->
                                </t>
                            </t>
                        </t>

                        <!-- CRM Lead Button removed as requested -->



                        <div class="mt-4 mb-5">
                            <a href="/" class="btn btn-primary">Zurück zur Startseite</a>
                            <a href="/premiumcar-erstanfrage-untervermittler" class="btn btn-outline-secondary ml-2">Neue Anfrage stellen</a>
                        </div>
                    </div>
                </div>
            </div>
        </t>
    </template>
</odoo>