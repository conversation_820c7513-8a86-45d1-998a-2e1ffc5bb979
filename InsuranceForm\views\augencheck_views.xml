<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_augencheck_request_form" model="ir.ui.view">
        <field name="name">augencheck.request.form</field>
        <field name="model">augencheck.request</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_submit" string="Submit" type="object"
                            class="oe_highlight" invisible="state != 'draft'"/>
                    <button name="action_process" string="Process" type="object"
                            class="oe_highlight" invisible="state != 'submitted'"/>
                    <field name="state" widget="statusbar" options="{'clickable': '1'}"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_lead" type="object" class="oe_stat_button" icon="fa-star"
                                invisible="not lead_id">
                            <field name="lead_id" string="CRM Lead" widget="statinfo"/>
                        </button>
                    </div>
                    <group>
                        <group string="Persönliche Daten">
                            <field name="salutation"/>
                            <field name="first_name"/>
                            <field name="last_name"/>
                            <field name="birth_date"/>
                            <field name="street"/>
                            <field name="street_number"/>
                            <field name="street_addition"/>
                            <field name="zip"/>
                            <field name="city"/>
                            <field name="country_id"/>
                        </group>
                        <group string="Kontaktinformationen">
                            <field name="email"/>
                            <field name="phone"/>
                            <field name="mobile"/>
                            <field name="contact_preference"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_augencheck_request_tree" model="ir.ui.view">
        <field name="name">augencheck.request.tree</field>
        <field name="model">augencheck.request</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="first_name"/>
                <field name="last_name"/>
                <field name="birth_date"/>
                <field name="email"/>
                <field name="phone"/>
                <field name="contact_preference"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="state"/>
                <field name="create_date"/>
            </tree>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_augencheck_request_search" model="ir.ui.view">
        <field name="name">augencheck.request.search</field>
        <field name="model">augencheck.request</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="first_name"/>
                <field name="last_name"/>
                <field name="birth_date"/>
                <field name="email"/>
                <field name="phone"/>
                <field name="mobile"/>
                <separator/>
                <filter string="Entwurf" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Eingereicht" name="submitted" domain="[('state', '=', 'submitted')]"/>
                <filter string="Bearbeitet" name="processed" domain="[('state', '=', 'processed')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="status" context="{'group_by': 'state'}"/>
                    <filter string="Datum" name="create_date" context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_augencheck_request" model="ir.actions.act_window">
        <field name="name">Augencheck Anfragen</field>
        <field name="res_model">augencheck.request</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Es wurden noch keine Augencheck-Anfragen erstellt
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <!-- Create a standalone Augencheck app -->
    <menuitem id="menu_augencheck_root" name="Augencheck" sequence="15"/>
    <menuitem id="menu_augencheck_request" parent="menu_augencheck_root" name="Anfragen" action="action_augencheck_request" sequence="10"/>

    <!-- Website Menu -->
    <record id="menu_website_augencheck" model="website.menu">
        <field name="name">Augencheck Anfrage AZ Kamppeter</field>
        <field name="url">/augencheck</field>
        <field name="parent_id" ref="website.main_menu"/>
        <field name="sequence" type="int">60</field>
    </record>
</odoo>