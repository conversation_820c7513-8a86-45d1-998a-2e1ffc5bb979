from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
import traceback
from psycopg2.errors import InFailedSqlTransaction
import io
import base64

# Try to import reportlab, but make it optional
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, BaseDocTemplate, PageTemplate, Frame, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.units import inch
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

_logger = logging.getLogger(__name__)

class AugencheckRequest(models.Model):
    _name = 'augencheck.request'
    _description = 'Augencheck Request'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string="Request Reference", required=True, copy=False, readonly=True,
                       default=lambda self: 'New')

    # Personal Information
    salutation = fields.Selection([
        ('mr', 'Herr'),
        ('mrs', 'Frau'),
        ('diverse', 'Divers'),
        ('company', 'Firma'),
        ('other', 'Sonstige')
    ], string='Anrede', required=True)
    first_name = fields.Char('Vorname', required=True)
    last_name = fields.Char('Nachname', required=True)
    birth_date = fields.Date('Geburtsdatum', required=True)
    street = fields.Char('Straße', required=True)
    street_number = fields.Char('Nr.', required=True)
    street_addition = fields.Char('Adresszusatz', required=True)
    zip = fields.Char('PLZ', required=True)
    city = fields.Char('Ort', required=True)
    country_id = fields.Many2one('res.country', string='Land', required=True,
                                 default=lambda self: self.env['res.country'].search([('code', '=', 'DE')], limit=1).id)

    # Contact Information
    email = fields.Char('E-Mail')
    phone = fields.Char('Telefonnummer')
    mobile = fields.Char('Mobilfunknummer')

    # Contact Preference
    contact_preference = fields.Selection([
        ('mobile', 'Mobile'),
        ('phone', 'Festnetz'),
        ('email', 'E-Mail')
    ], string='Bevorzugte Kontaktart')

    # Status
    state = fields.Selection([
        ('draft', 'Entwurf'),
        ('submitted', 'Eingereicht'),
        ('processed', 'Bearbeitet')
    ], default='draft', string='Status', tracking=True)

    # Link to CRM Lead
    lead_id = fields.Many2one('crm.lead', string='Related Lead', ondelete='set null')

    # Link to Contact
    partner_id = fields.Many2one('res.partner', string='Related Contact', ondelete='set null')

    # Company field for multi-company access control
    company_id = fields.Many2one('res.company', string='Company',
                                default=lambda self: self.env.company,
                                required=True,
                                help="Company that this request belongs to")

    # SQL constraints
    _sql_constraints = [
        ('company_id_required', 'CHECK (company_id IS NOT NULL)',
         'Company is required for all insurance requests.'),
    ]

    @api.model
    def create(self, vals):
        """
        Create a new Augencheck request with improved error handling and transaction management.
        This method handles the creation of related partner and CRM lead records.
        """
        # Use a savepoint to allow partial rollback if needed
        cr = self.env.cr

        try:
            # Generate a timestamp-based reference number (revert to original working logic)
            if vals.get('name', 'New') == 'New':
                # Generate timestamp part
                timestamp = fields.Datetime.now().strftime('%Y%m%d%H%M%S')
                # Use timestamp-based reference (same as premiumcar_endkunden for consistency)
                vals['name'] = f"AC-{timestamp}"
                _logger.info(f"Generated Augencheck reference number: {vals['name']}")

            # Create a contact record for this customer
            # Use the same reference number for consistency
            reference_code = vals.get('name', 'New')

            # Use clean name without timestamp
            clean_name = f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip()
            if not clean_name:
                _logger.warning("Missing first_name or last_name for contact creation")
                clean_name = f"Unknown Contact ({timestamp})"

            partner_vals = {
                'name': clean_name,
                'ref': reference_code,
                'street': vals.get('street', ''),
                'street2': vals.get('street_addition', ''),
                'zip': vals.get('zip', ''),
                'city': vals.get('city', ''),
                'country_id': vals.get('country_id'),
                'email': vals.get('email', ''),
                'phone': vals.get('phone', ''),
                'mobile': vals.get('mobile', ''),
                'type': 'contact',
                'company_type': 'person',
                'comment': f"Created from Augencheck form {vals.get('name', 'New')}",
            }

            # Set the proper title based on salutation
            if vals.get('salutation') == 'mr':
                title = self.env.ref('base.res_partner_title_mister', False)
                if title:
                    partner_vals['title'] = title.id
            elif vals.get('salutation') == 'mrs':
                title = self.env.ref('base.res_partner_title_madam', False)
                if title:
                    partner_vals['title'] = title.id

            # Create a savepoint before partner operations
            cr.execute('SAVEPOINT augencheck_partner_savepoint')

            # Check if a partner with this email or phone already exists
            existing_partner = False
            if partner_vals.get('email'):
                existing_partner = self.env['res.partner'].sudo().search([
                    ('email', '=', partner_vals.get('email'))
                ], limit=1)
                if existing_partner:
                    _logger.info(f"Found existing partner with email {partner_vals.get('email')}: ID {existing_partner.id}")

            if not existing_partner and partner_vals.get('phone'):
                existing_partner = self.env['res.partner'].sudo().search([
                    ('phone', '=', partner_vals.get('phone'))
                ], limit=1)
                if existing_partner:
                    _logger.info(f"Found existing partner with phone {partner_vals.get('phone')}: ID {existing_partner.id}")

            # Create or link the partner
            try:
                if existing_partner:
                    # Use existing partner
                    _logger.info(f"Using existing partner with ID {existing_partner.id} for Augencheck form")
                    partner_id = existing_partner.id
                else:
                    # Create new partner
                    partner = self.env['res.partner'].with_context(disable_duplicate_check=True).sudo().create(partner_vals)
                    partner_id = partner.id
                    _logger.info(f"Created new partner with ID {partner_id} for Augencheck form")

                # Set the partner_id in the record
                vals['partner_id'] = partner_id
            except Exception as e:
                _logger.error(f"Error creating contact for Augencheck form: {e}")
                _logger.error(f"Traceback: {traceback.format_exc()}")
                # Rollback to savepoint to avoid transaction issues
                cr.execute('ROLLBACK TO SAVEPOINT augencheck_partner_savepoint')
                # Continue without partner
                _logger.info("Continuing without partner due to error")

            # Create a savepoint before lead operations
            cr.execute('SAVEPOINT augencheck_lead_savepoint')

            # Create CRM Lead if none exists
            if not vals.get('lead_id'):
                try:
                    # Prepare data for mapper
                    form_data = {
                        'first_name': vals.get('first_name'),
                        'last_name': vals.get('last_name'),
                        'email': vals.get('email'),
                        'phone': vals.get('phone'),
                        'mobile': vals.get('mobile'),
                        # Add address information
                        'street': vals.get('street'),
                        'street_number': vals.get('street_number'),
                        'street_addition': vals.get('street_addition'),
                        'zip': vals.get('zip'),
                        'city': vals.get('city'),
                        'country_id': vals.get('country_id'),
                        # Add salutation for gender mapping
                        'salutation': vals.get('salutation'),
                        # Add birth date
                        'birth_date': vals.get('birth_date'),
                    }

                    # Check if opportunity mapper exists
                    mapper_exists = False
                    try:
                        mapper_exists = self.env['opportunity.mapper'] and True
                        _logger.info("opportunity.mapper model is available")
                    except Exception as mapper_e:
                        _logger.error(f"Error checking for opportunity.mapper model: {mapper_e}")

                    # Prepare lead values
                    full_name = f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip()

                    if mapper_exists:
                        try:
                            # Use mapper to map fields
                            _logger.info(f"Attempting to map form data to opportunity with mapper: {form_data}")
                            lead_vals = self.env['opportunity.mapper'].map_form_to_opportunity(form_data, 'augencheck')
                            _logger.info(f"Successfully mapped form data to lead values: {lead_vals}")

                            # Set lead name if not set by mapper
                            if not lead_vals.get('name'):
                                lead_vals['name'] = f"Augencheck Anfrage von {full_name}"
                        except Exception as mapper_e:
                            _logger.error(f"Error using opportunity mapper: {mapper_e}")
                            _logger.error(f"Traceback: {traceback.format_exc()}")

                            # Fallback to basic lead creation
                            _logger.info("Falling back to basic lead creation")

                            # Get CRM fields for validation
                            crm_fields = self.env['crm.lead'].sudo().fields_get()

                            # Basic lead values that are standard in Odoo
                            lead_vals = {
                                'name': f"Augencheck Anfrage von {full_name}",
                                'type': 'lead',
                                'contact_name': full_name,
                                'email_from': vals.get('email', ''),
                                'phone': vals.get('phone', ''),
                                'description': f"Augencheck Anfrage von {full_name}",
                            }

                            # Handle gender mapping with validation (same logic as premiumcar.request)
                            gender_field = None
                            if 'gender' in crm_fields:
                                gender_field = 'gender'
                            elif 'policy_holder_gender' in crm_fields:
                                gender_field = 'policy_holder_gender'

                            if gender_field:
                                # Get valid selection options
                                field_selection = crm_fields.get(gender_field, {}).get('selection', [])
                                field_options = [opt[0] for opt in field_selection] if field_selection else []
                                _logger.debug(f"Available {gender_field} options: {field_options}")

                                # Map salutation to gender
                                if vals.get('salutation') == 'mr':
                                    gender_value = 'male'
                                elif vals.get('salutation') == 'mrs':
                                    gender_value = 'female'
                                else:
                                    # For other salutations, try different possible values
                                    if 'other' in field_options:
                                        gender_value = 'other'
                                    elif 'others' in field_options:
                                        gender_value = 'others'
                                    elif 'diverse' in field_options:
                                        gender_value = 'diverse'
                                    else:
                                        # Use the first available option as fallback
                                        gender_value = field_options[0] if field_options else 'other'
                                        _logger.warning(f"No suitable gender option found for salutation '{vals.get('salutation')}', using fallback: '{gender_value}'")

                                # Only set if the value is valid
                                if gender_value in field_options:
                                    lead_vals[gender_field] = gender_value
                                    _logger.debug(f"Set {gender_field} to: {gender_value}")
                                else:
                                    _logger.warning(f"Gender value '{gender_value}' not available in {gender_field} options: {field_options}")

                            # Add birth date field if it exists in the model
                            if 'birth_date' in crm_fields and vals.get('birth_date'):
                                lead_vals['birth_date'] = vals.get('birth_date')
                            elif 'policy_holder_dob' in crm_fields and vals.get('birth_date'):
                                lead_vals['policy_holder_dob'] = vals.get('birth_date')
                            elif 'dob_policy_holder' in crm_fields and vals.get('birth_date'):
                                lead_vals['dob_policy_holder'] = vals.get('birth_date')

                            # Check if policy_category field exists before setting it
                            if 'policy_category' in crm_fields:
                                lead_vals['policy_category'] = 'health'
                            elif 'insurance_category_id' in crm_fields:
                                # Try to find the health insurance category
                                try:
                                    health_category = self.env['insurance.category'].sudo().search(
                                        [('category', '=', 'health')], limit=1)
                                    if health_category:
                                        lead_vals['insurance_category_id'] = health_category.id
                                except Exception as cat_e:
                                    _logger.warning(f"Could not set insurance category: {cat_e}")
                    else:
                        # Fallback to basic lead creation
                        _logger.info("Using basic lead creation (no mapper available)")

                        # Get the reference number for the lead description
                        reference_number = vals.get('name', 'New')

                        # Get CRM fields for validation
                        crm_fields = self.env['crm.lead'].sudo().fields_get()

                        # Basic lead values that are standard in Odoo
                        lead_vals = {
                            'name': f"Augencheck Anfrage von {full_name}",
                            'type': 'lead',
                            'contact_name': full_name,
                            'email_from': vals.get('email', ''),
                            'phone': vals.get('phone', ''),
                            'description': f"Augencheck Anfrage von {full_name} - Referenznummer: {reference_number}",
                        }

                        # Add website_ref_number if it exists in the model
                        if 'website_ref_number' in crm_fields:
                            lead_vals['website_ref_number'] = reference_number

                        # Handle gender mapping with validation (same logic as above)
                        gender_field = None
                        if 'gender' in crm_fields:
                            gender_field = 'gender'
                        elif 'policy_holder_gender' in crm_fields:
                            gender_field = 'policy_holder_gender'

                        if gender_field:
                            # Get valid selection options
                            field_selection = crm_fields.get(gender_field, {}).get('selection', [])
                            field_options = [opt[0] for opt in field_selection] if field_selection else []
                            _logger.debug(f"Available {gender_field} options: {field_options}")

                            # Map salutation to gender
                            if vals.get('salutation') == 'mr':
                                gender_value = 'male'
                            elif vals.get('salutation') == 'mrs':
                                gender_value = 'female'
                            else:
                                # For other salutations, try different possible values
                                if 'other' in field_options:
                                    gender_value = 'other'
                                elif 'others' in field_options:
                                    gender_value = 'others'
                                elif 'diverse' in field_options:
                                    gender_value = 'diverse'
                                else:
                                    # Use the first available option as fallback
                                    gender_value = field_options[0] if field_options else 'other'
                                    _logger.warning(f"No suitable gender option found for salutation '{vals.get('salutation')}', using fallback: '{gender_value}'")

                            # Only set if the value is valid
                            if gender_value in field_options:
                                lead_vals[gender_field] = gender_value
                                _logger.debug(f"Set {gender_field} to: {gender_value}")
                            else:
                                _logger.warning(f"Gender value '{gender_value}' not available in {gender_field} options: {field_options}")

                        # Add birth date field if it exists in the model
                        if 'birth_date' in crm_fields and vals.get('birth_date'):
                            lead_vals['birth_date'] = vals.get('birth_date')
                        elif 'policy_holder_dob' in crm_fields and vals.get('birth_date'):
                            lead_vals['policy_holder_dob'] = vals.get('birth_date')
                        elif 'dob_policy_holder' in crm_fields and vals.get('birth_date'):
                            lead_vals['dob_policy_holder'] = vals.get('birth_date')

                        # Check if policy_category field exists before setting it
                        if 'policy_category' in crm_fields:
                            lead_vals['policy_category'] = 'health'
                        elif 'insurance_category_id' in crm_fields:
                            # Try to find the health insurance category
                            try:
                                health_category = self.env['insurance.category'].sudo().search(
                                    [('category', '=', 'health')], limit=1)
                                if health_category:
                                    lead_vals['insurance_category_id'] = health_category.id
                            except Exception as cat_e:
                                _logger.warning(f"Could not set insurance category: {cat_e}")

                    # Link to partner if created
                    if vals.get('partner_id'):
                        lead_vals['partner_id'] = vals.get('partner_id')
                        _logger.info(f"Linking lead to partner ID: {vals.get('partner_id')}")

                    # Ensure required fields are present
                    if not lead_vals.get('name'):
                        lead_vals['name'] = f"Augencheck Anfrage von {full_name}"

                    if not lead_vals.get('type'):
                        lead_vals['type'] = 'lead'

                    # Log the final lead values before creation
                    _logger.info(f"Final lead values for creation: {lead_vals}")

                    # Create lead with extensive diagnostic logging that will be visible in the UI
                    debug_info = f"About to create CRM lead with values: {lead_vals}\n"

                    # Create a note in the chatter that will be visible in the UI
                    self = self.with_context(tracking_disable=True)  # Disable tracking temporarily

                    # Check if we have access to create CRM leads
                    try:
                        can_create = self.env['crm.lead'].check_access_rights('create', raise_exception=False)
                        debug_info += f"User has create access rights for crm.lead: {can_create}\n"
                    except Exception as access_e:
                        debug_info += f"Error checking access rights: {access_e}\n"

                    # Check if all required fields are present
                    try:
                        crm_fields = self.env['crm.lead'].fields_get()
                        required_fields = [f for f, attrs in crm_fields.items()
                                          if attrs.get('required') and not attrs.get('readonly')]
                        missing_fields = [f for f in required_fields if f not in lead_vals]
                        if missing_fields:
                            debug_info += f"Missing required fields for crm.lead: {missing_fields}\n"
                            # Try to add default values for missing required fields
                            for field in missing_fields:
                                if field == 'name' and not lead_vals.get('name'):
                                    lead_vals['name'] = f"Augencheck Anfrage {fields.Datetime.now()}"
                                elif field == 'type' and not lead_vals.get('type'):
                                    lead_vals['type'] = 'lead'
                    except Exception as fields_e:
                        debug_info += f"Error checking required fields: {fields_e}\n"

                    # Log to standard logger as well
                    _logger.info(debug_info)

                    # Try to create the lead with detailed error logging
                    try:
                        lead = self.env['crm.lead'].sudo().create(lead_vals)
                        vals['lead_id'] = lead.id
                        debug_info += f"Successfully created CRM lead with ID {lead.id} for Augencheck form\n"

                        # Set company_id from the created lead's policy provider
                        if lead.policy_provider_id:
                            vals['company_id'] = lead.policy_provider_id.id
                            debug_info += f"Set company_id to {lead.policy_provider_id.name} from CRM lead\n"

                        # Force a commit to ensure the lead is saved
                        self.env.cr.commit()
                    except Exception as create_e:
                        error_msg = str(create_e)
                        debug_info += f"CRITICAL ERROR creating CRM lead: {error_msg}\n"
                        debug_info += f"Detailed error traceback: {traceback.format_exc()}\n"

                        # Try to identify specific error type
                        if "access right" in error_msg.lower() or "access error" in error_msg.lower():
                            debug_info += "This appears to be a permissions issue\n"
                        elif "null value in column" in error_msg.lower():
                            debug_info += "This appears to be a missing required field issue\n"
                        elif "foreign key constraint" in error_msg.lower():
                            debug_info += "This appears to be a foreign key constraint issue\n"

                        # Log the final error information
                        _logger.error(debug_info)

                        # Re-raise to be caught by the outer exception handler
                        raise
                    finally:
                        # Add the debug info to the record's chatter for UI visibility
                        try:
                            # This will be visible in the UI even without server log access
                            message = f"<pre>{debug_info}</pre>"
                            self.env['mail.message'].sudo().create({
                                'body': message,
                                'message_type': 'comment',
                                'model': 'augencheck.request',
                                'res_id': self.id if self.id else 0,  # Will be set later if not yet created
                                'subtype_id': self.env.ref('mail.mt_note').id,
                                'author_id': self.env.user.partner_id.id,
                            })
                        except Exception as msg_e:
                            _logger.error(f"Could not create debug message: {msg_e}")
                except Exception as e:
                    _logger.error(f"Error creating CRM lead from augencheck request: {e}")
                    _logger.error(f"Traceback: {traceback.format_exc()}")
                    # Rollback to savepoint to avoid transaction issues
                    cr.execute('ROLLBACK TO SAVEPOINT augencheck_lead_savepoint')
                    # Continue without lead
                    _logger.info("Continuing without lead due to error")

            # Create the augencheck request
            try:
                result = super(AugencheckRequest, self).create(vals)
                _logger.info(f"Successfully created Augencheck request with ID {result.id}")
                return result
            except Exception as e:
                _logger.error(f"Error creating Augencheck request: {e}")
                _logger.error(f"Traceback: {traceback.format_exc()}")
                # Re-raise the exception to show error to user
                raise

        except Exception as e:
            _logger.error(f"Unexpected error in Augencheck request creation: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            # Re-raise with a more user-friendly message
            raise UserError(_("Could not create Augencheck request. Please try again or contact support."))

    def action_submit(self):
        """Submit the request and update its status with improved error handling"""
        cr = self.env.cr

        for rec in self:
            try:
                # Create a savepoint before updating state
                cr.execute('SAVEPOINT augencheck_submit_savepoint')

                # Update state
                rec.state = 'submitted'
                _logger.info(f"Updated Augencheck request {rec.id} state to 'submitted'")

                # Ensure lead is updated with latest information
                if rec.lead_id:
                    try:
                        # Create a savepoint before updating lead
                        cr.execute('SAVEPOINT augencheck_lead_update_savepoint')

                        # Update lead with current values
                        lead_vals = {
                            'contact_name': f"{rec.first_name} {rec.last_name}".strip(),
                            'email_from': rec.email,
                            'phone': rec.phone,
                            'mobile': rec.mobile,
                        }

                        # Update the lead
                        rec.lead_id.sudo().write(lead_vals)
                        _logger.info(f"Updated CRM lead {rec.lead_id.id} with latest information")
                    except Exception as e:
                        _logger.error(f"Error updating CRM lead from augencheck request: {e}")
                        _logger.error(f"Traceback: {traceback.format_exc()}")
                        # Rollback to savepoint to avoid transaction issues
                        cr.execute('ROLLBACK TO SAVEPOINT augencheck_lead_update_savepoint')
                        # Continue without updating lead
                        _logger.info("Continuing without updating lead due to error")
            except Exception as e:
                _logger.error(f"Error submitting Augencheck request: {e}")
                _logger.error(f"Traceback: {traceback.format_exc()}")
                # Rollback to savepoint
                cr.execute('ROLLBACK TO SAVEPOINT augencheck_submit_savepoint')
                # Raise user-friendly error
                raise UserError(_("Could not submit Augencheck request. Please try again or contact support."))

    def action_process(self):
        """Mark the request as processed with improved error handling"""
        cr = self.env.cr

        for rec in self:
            try:
                # Create a savepoint before updating state
                cr.execute('SAVEPOINT augencheck_process_savepoint')

                # Update state
                rec.state = 'processed'
                _logger.info(f"Updated Augencheck request {rec.id} state to 'processed'")

                # Convert lead to opportunity if it exists
                if rec.lead_id and rec.lead_id.type == 'lead':
                    try:
                        # Create a savepoint before converting lead
                        cr.execute('SAVEPOINT augencheck_lead_convert_savepoint')

                        # First, check which fields actually exist in the model
                        lead_fields = rec.lead_id.fields_get()
                        valid_fields = {}

                        # Initialize empty mandatory fields dictionary
                        mandatory_fields = {}

                        # Check for birth date field and add it if it exists
                        if rec.birth_date:
                            if 'birth_date' in lead_fields:
                                mandatory_fields['birth_date'] = rec.birth_date
                            elif 'policy_holder_dob' in lead_fields:
                                mandatory_fields['policy_holder_dob'] = rec.birth_date
                            elif 'dob_policy_holder' in lead_fields:
                                mandatory_fields['dob_policy_holder'] = rec.birth_date
                        else:
                            # Fallback to today if no birth date is set
                            if 'birth_date' in lead_fields:
                                mandatory_fields['birth_date'] = fields.Date.today()
                            elif 'policy_holder_dob' in lead_fields:
                                mandatory_fields['policy_holder_dob'] = fields.Date.today()
                            elif 'dob_policy_holder' in lead_fields:
                                mandatory_fields['dob_policy_holder'] = fields.Date.today()

                        # Check for gender field and add it if it exists
                        gender_field = None
                        if 'gender' in lead_fields:
                            gender_field = 'gender'
                        elif 'policy_holder_gender' in lead_fields:
                            gender_field = 'policy_holder_gender'

                        # Only set gender if the field exists and isn't already set
                        if gender_field:
                            try:
                                current_gender = getattr(rec.lead_id, gender_field, False)
                                if not current_gender:
                                    # Map salutation to gender
                                    gender_value = 'other'  # Default
                                    if rec.salutation == 'mr':
                                        gender_value = 'male'
                                    elif rec.salutation == 'mrs':
                                        gender_value = 'female'
                                    mandatory_fields[gender_field] = gender_value
                                else:
                                    _logger.info(f"{gender_field} already set to {current_gender}, preserving value")
                            except Exception as e:
                                _logger.warning(f"Error checking {gender_field} field: {e}")

                        # Check for policy category field and add it if it exists
                        if 'policy_category' in lead_fields:
                            mandatory_fields['policy_category'] = 'health'
                        elif 'insurance_category_id' in lead_fields:
                            # Try to find the health insurance category
                            try:
                                health_category = self.env['insurance.category'].sudo().search(
                                    [('category', '=', 'health')], limit=1)
                                if health_category:
                                    mandatory_fields['insurance_category_id'] = health_category.id
                            except Exception as cat_e:
                                _logger.warning(f"Could not set insurance category: {cat_e}")

                        for field, value in mandatory_fields.items():
                            if field in lead_fields:
                                # Check if the field is a selection field
                                if lead_fields[field].get('type') == 'selection':
                                    # Get valid selection options
                                    selection_options = [opt[0] for opt in lead_fields[field].get('selection', [])]
                                    if selection_options and value not in selection_options:
                                        # Use the first option if our default is not valid
                                        valid_fields[field] = selection_options[0]
                                    else:
                                        valid_fields[field] = value
                                else:
                                    valid_fields[field] = value

                        # Update the lead with default values for mandatory fields
                        if valid_fields:
                            rec.lead_id.sudo().write(valid_fields)
                            _logger.info(f"Added default values for mandatory fields: {valid_fields}")

                        # Now convert lead to opportunity
                        rec.lead_id.sudo().convert_opportunity(
                            rec.lead_id.partner_id.id if rec.lead_id.partner_id else False,
                            False
                        )
                        _logger.info(f"Converted lead {rec.lead_id.id} to opportunity")
                    except Exception as e:
                        _logger.error(f"Error converting lead to opportunity: {e}")
                        _logger.error(f"Traceback: {traceback.format_exc()}")
                        # Rollback to savepoint to avoid transaction issues
                        cr.execute('ROLLBACK TO SAVEPOINT augencheck_lead_convert_savepoint')
                        # Continue without converting lead
                        _logger.info("Continuing without converting lead due to error")

                        # Add error details to chatter for UI visibility
                        try:
                            error_msg = f"<pre>Error converting to opportunity: {e}\n\n{traceback.format_exc()}</pre>"
                            self.env['mail.message'].sudo().create({
                                'body': error_msg,
                                'message_type': 'comment',
                                'model': 'augencheck.request',
                                'res_id': rec.id,
                                'subtype_id': self.env.ref('mail.mt_note').id,
                                'author_id': self.env.user.partner_id.id,
                            })
                        except Exception as msg_e:
                            _logger.error(f"Could not create error message: {msg_e}")
            except Exception as e:
                _logger.error(f"Error processing Augencheck request: {e}")
                _logger.error(f"Traceback: {traceback.format_exc()}")
                # Rollback to savepoint
                cr.execute('ROLLBACK TO SAVEPOINT augencheck_process_savepoint')
                # Raise user-friendly error
                raise UserError(_("Could not process Augencheck request. Please try again or contact support."))

    def action_view_lead(self):
        """Open the related CRM lead in a form view"""
        self.ensure_one()
        if not self.lead_id:
            raise UserError(_("No CRM lead is linked to this request."))

        return {
            'name': _('CRM Lead'),
            'view_mode': 'form',
            'res_model': 'crm.lead',
            'res_id': self.lead_id.id,
            'type': 'ir.actions.act_window',
        }

    def generate_pdf_summary(self):
        """Generate a PDF summary of the Augencheck request"""
        self.ensure_one()

        if not REPORTLAB_AVAILABLE:
            _logger.error("ReportLab is not available. Cannot generate PDF.")
            raise UserError(_("PDF generation is not available. Please contact your administrator."))

        try:
            # Create a buffer to hold the PDF
            buffer = io.BytesIO()

            # Create the PDF document with custom page template for logo support
            doc = BaseDocTemplate(buffer, pagesize=A4, leftMargin=0.75*inch, rightMargin=0.75*inch)

            # Create a frame for the content
            frame = Frame(0.75*inch, 0.75*inch, A4[0] - 1.5*inch, A4[1] - 1.5*inch,
                         leftPadding=0, bottomPadding=0, rightPadding=0, topPadding=0)

            # Create page template with header/footer function
            page_template = PageTemplate(id='main', frames=[frame], onPage=self._create_header_footer)
            doc.addPageTemplates([page_template])

            story = []

            # Get styles
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                textColor=colors.darkred,
                alignment=1  # Center alignment
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                textColor=colors.darkblue
            )

            normal_style = styles['Normal']

            # Add large spacer to push title below logo and improve page breaks
            story.append(Spacer(1, 80))

            # Title
            story.append(Paragraph("Augencheck Anfrage - Zusammenfassung", title_style))
            story.append(Spacer(1, 20))

            # Reference number
            story.append(Paragraph(f"<b>Referenznummer:</b> {self.name}", normal_style))
            story.append(Spacer(1, 20))

            # Personal Information Section
            story.append(Paragraph("Persönliche Daten", heading_style))

            personal_data = [
                ['Anrede:', self._get_salutation_display()],
                ['Vorname:', self.first_name or ''],
                ['Nachname:', self.last_name or ''],
                ['Geburtsdatum:', self.birth_date.strftime('%d.%m.%Y') if self.birth_date else ''],
            ]

            personal_table = Table(personal_data, colWidths=[2*inch, 4*inch])
            personal_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))

            story.append(personal_table)
            story.append(Spacer(1, 20))

            # Address Information Section
            story.append(Paragraph("Adresse", heading_style))

            address_data = [
                ['Straße:', f"{self.street or ''} {self.street_number or ''}".strip()],
                ['Adresszusatz:', self.street_addition or ''],
                ['PLZ:', self.zip or ''],
                ['Ort:', self.city or ''],
                ['Land:', self.country_id.name if self.country_id else ''],
            ]

            address_table = Table(address_data, colWidths=[2*inch, 4*inch])
            address_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))

            story.append(address_table)
            story.append(Spacer(1, 20))

            # Contact Information Section
            story.append(Paragraph("Kontaktdaten", heading_style))

            contact_data = [
                ['E-Mail:', self.email or ''],
                ['Telefonnummer:', self.phone or ''],
                ['Mobilfunknummer:', self.mobile or ''],
                ['Bevorzugte Kontaktart:', self._get_contact_preference_display()],
            ]

            contact_table = Table(contact_data, colWidths=[2*inch, 4*inch])
            contact_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))

            story.append(contact_table)
            story.append(Spacer(1, 20))

            # Footer
            story.append(Spacer(1, 30))
            footer_text = f"Erstellt am: {fields.Datetime.now().strftime('%d.%m.%Y %H:%M')} Uhr"
            story.append(Paragraph(footer_text, normal_style))

            # Build PDF
            doc.build(story)

            # Get the PDF data
            pdf_data = buffer.getvalue()
            buffer.close()

            return base64.b64encode(pdf_data)

        except Exception as e:
            _logger.error(f"Error generating PDF: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            raise UserError(_("Error generating PDF. Please try again or contact support."))

    def _get_salutation_display(self):
        """Get display value for salutation"""
        salutation_dict = dict(self._fields['salutation'].selection)
        return salutation_dict.get(self.salutation, '')

    def _get_contact_preference_display(self):
        """Get display value for contact preference"""
        if not self.contact_preference:
            return ''
        preference_dict = dict(self._fields['contact_preference'].selection)
        return preference_dict.get(self.contact_preference, '')

    def _get_logo_path(self):
        """Get the path to the logo file for PDF header"""
        import os

        try:
            _logger.info("=== GETTING AUGENCHECK LOGO PATH ===")

            # List of possible logo paths to try
            possible_paths = []

            # Method 1: Using __file__ to get current module directory
            try:
                current_dir = os.path.dirname(os.path.abspath(__file__))
                _logger.info(f"Current module directory: {current_dir}")

                # Go up to InsuranceForm directory, then to static/img
                insurance_form_dir = os.path.dirname(current_dir)  # Go up from models/ to InsuranceForm/
                possible_paths.append(os.path.join(insurance_form_dir, 'static', 'img', 'augencheck_logo.png'))

            except Exception as e:
                _logger.error(f"Error getting current directory: {e}")

            # Method 2: Using Odoo's get_module_path if available
            try:
                from odoo.modules import get_module_path
                module_path_odoo = get_module_path('InsuranceForm')
                if module_path_odoo:
                    possible_paths.append(os.path.join(module_path_odoo, 'static', 'img', 'augencheck_logo.png'))
            except Exception as e:
                _logger.error(f"Error getting module path via Odoo: {e}")

            # Method 3: Try relative paths from common Odoo locations
            try:
                import odoo
                odoo_dir = os.path.dirname(odoo.__file__)
                addons_paths = [
                    os.path.join(odoo_dir, '..', 'addons', 'InsuranceForm', 'static', 'img', 'augencheck_logo.png'),
                    os.path.join(odoo_dir, 'addons', 'InsuranceForm', 'static', 'img', 'augencheck_logo.png'),
                ]
                possible_paths.extend(addons_paths)
            except Exception as e:
                _logger.error(f"Error getting Odoo addons paths: {e}")

            _logger.info(f"Possible augencheck logo paths to check: {possible_paths}")

            # Check each path and return the first one that exists
            for path in possible_paths:
                abs_path = os.path.abspath(path)
                _logger.info(f"Checking augencheck logo path: {abs_path}")
                if os.path.exists(abs_path):
                    _logger.info(f"Found augencheck logo at: {abs_path}")
                    return abs_path
                else:
                    _logger.info(f"Augencheck logo not found at: {abs_path}")

            _logger.warning("Augencheck logo file not found in any of the expected locations")
            return None

        except Exception as e:
            _logger.error(f"Error getting augencheck logo path: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _create_header_footer(self, canvas, doc):
        """Create header with logo and footer for PDF pages"""
        import os
        try:
            _logger.info("=== AUGENCHECK HEADER FUNCTION CALLED ===")

            # Get logo path
            logo_path = self._get_logo_path()

            if logo_path and os.path.exists(logo_path):
                _logger.info(f"Attempting to draw augencheck logo from: {logo_path}")

                # Draw logo in top-right corner with professional sizing
                # Position: 0.4 inch from right edge, 0.25 inch from top
                # Title will be positioned below the logo for better visual hierarchy
                logo_width = 2.0 * inch   # Same size as other forms (67% larger than original)
                logo_height = 1.0 * inch  # Same size as other forms (67% larger than original)

                x_position = A4[0] - 0.4*inch - logo_width  # Right edge minus padding minus logo width
                y_position = A4[1] - 0.25*inch - logo_height  # Top edge minus padding minus logo height

                _logger.info(f"Drawing augencheck logo at position: x={x_position}, y={y_position}, width={logo_width}, height={logo_height}")

                canvas.drawImage(logo_path, x_position, y_position,
                               width=logo_width, height=logo_height,
                               preserveAspectRatio=True, mask='auto')

                _logger.info("Augencheck logo successfully drawn to PDF")
            else:
                _logger.info("No valid augencheck logo file found, skipping logo in PDF header")

        except Exception as e:
            # Log error but don't fail PDF generation
            _logger.error(f"Error adding augencheck logo to PDF: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            pass