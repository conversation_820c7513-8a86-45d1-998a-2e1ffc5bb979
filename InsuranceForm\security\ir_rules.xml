<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data noupdate="0">

        <!-- Record Rules for Premium Car Request Model -->
        <record id="premium_car_request_company_rule_v2" model="ir.rule">
            <field name="name">Premium Car Request: Company Access V2</field>
            <field name="model_id" ref="model_premium_car_request"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- Record Rules for Premium Car Untervermittler Model -->
        <record id="premiumcar_untervermittler_company_rule_v2" model="ir.rule">
            <field name="name">Premium Car Untervermittler: Company Access V2</field>
            <field name="model_id" ref="model_premiumcar_untervermittler"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- Record Rules for Premium Car Endkunden Model -->
        <record id="premiumcar_endkunden_company_rule_v2" model="ir.rule">
            <field name="name">Premium Car Endkunden: Company Access V2</field>
            <field name="model_id" ref="model_premiumcar_endkunden"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- Record Rules for Premium Car Erstanfrage Endkunden Intern Model -->
        <record id="premiumcar_erstanfrage_endkunden_intern_company_rule_v2" model="ir.rule">
            <field name="name">Premium Car Erstanfrage Endkunden Intern: Company Access V2</field>
            <field name="model_id" ref="model_premiumcar_erstanfrage_endkunden_intern"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- Record Rules for Augencheck Request Model -->
        <record id="augencheck_request_company_rule_v2" model="ir.rule">
            <field name="name">Augencheck Request: Company Access V2</field>
            <field name="model_id" ref="model_augencheck_request"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read" eval="1"/>
            <field name="perm_write" eval="1"/>
            <field name="perm_create" eval="1"/>
            <field name="perm_unlink" eval="1"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>





    </data>
</odoo>
