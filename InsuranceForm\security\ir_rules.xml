<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data noupdate="0">

        <!-- COMPANY SECURITY RULES FOR INSURANCE FORMS -->

        <!-- Premium Car Request: Company Security -->
        <record id="premium_car_request_company_rule_final" model="ir.rule">
            <field name="name">Premium Car Request: Multi-Company Rule</field>
            <field name="model_id" ref="model_premium_car_request"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="active" eval="True"/>
        </record>

        <!-- Premium Car Untervermittler: Company Security -->
        <record id="premiumcar_untervermittler_company_rule" model="ir.rule">
            <field name="name">Premium Car Untervermittler: Multi-Company Rule</field>
            <field name="model_id" ref="model_premiumcar_untervermittler"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- Premium Car Endkunden: Company Security -->
        <record id="premiumcar_endkunden_company_rule" model="ir.rule">
            <field name="name">Premium Car Endkunden: Multi-Company Rule</field>
            <field name="model_id" ref="model_premiumcar_endkunden"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- Premium Car Erstanfrage Endkunden Intern: Company Security -->
        <record id="premiumcar_erstanfrage_endkunden_intern_company_rule" model="ir.rule">
            <field name="name">Premium Car Erstanfrage Endkunden Intern: Multi-Company Rule</field>
            <field name="model_id" ref="model_premiumcar_erstanfrage_endkunden_intern"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- Augencheck Request: Company Security -->
        <record id="augencheck_request_company_rule" model="ir.rule">
            <field name="name">Augencheck Request: Multi-Company Rule</field>
            <field name="model_id" ref="model_augencheck_request"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>





    </data>
</odoo>
