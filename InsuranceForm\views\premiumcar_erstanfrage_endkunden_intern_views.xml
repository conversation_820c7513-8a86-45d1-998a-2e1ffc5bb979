<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_premiumcar_erstanfrage_endkunden_intern_form" model="ir.ui.view">
        <field name="name">premiumcar.erstanfrage.endkunden.intern.form</field>
        <field name="model">premiumcar.erstanfrage.endkunden.intern</field>
        <field name="arch" type="xml">
            <form string="PremiumCar Erstanfrage Endkunden Intern">
                <header>
                    <field name="state" widget="statusbar" options="{'clickable': '1'}"/>
                    <button name="action_view_lead" string="View CRM Lead" type="object" class="oe_highlight" invisible="not lead_id"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group string="Versicherungsinformationen">
                            <field name="insurance_start_date"/>
                            <field name="lead_id" readonly="1"/>
                            <field name="partner_id" readonly="1"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Versicherungsnehmer">
                            <group>
                                <group>
                                    <field name="salutation"/>
                                    <field name="first_name"/>
                                    <field name="last_name"/>
                                    <field name="street"/>
                                    <field name="street_number"/>
                                    <field name="street_addition"/>
                                    <field name="zip"/>
                                    <field name="city"/>
                                    <field name="country_id"/>
                                </group>
                                <group>
                                    <field name="email"/>
                                    <field name="phone"/>
                                    <field name="mobile"/>
                                    <field name="is_owner_different"/>
                                </group>
                            </group>
                            <group invisible="is_owner_different != 'yes'">
                                <group string="Halter Informationen">
                                    <field name="owner_salutation"/>
                                    <field name="owner_first_name"/>
                                    <field name="owner_last_name"/>
                                    <field name="owner_street"/>
                                    <field name="owner_street_number"/>
                                    <field name="owner_street_addition"/>
                                    <field name="owner_zip"/>
                                    <field name="owner_city"/>
                                    <field name="owner_country_id"/>
                                </group>
                                <group string="Kontaktinformationen Halter">
                                    <field name="owner_email"/>
                                    <field name="owner_phone"/>
                                    <field name="owner_mobile"/>
                                </group>
                            </group>
                        </page>
                        <page string="Fahrzeug">
                            <group>
                                <group>
                                    <field name="car_manufacturer_id"/>
                                    <field name="car_type"/>
                                    <field name="license_plate"/>
                                    <field name="is_season_registration"/>
                                    <field name="season_start_date" invisible="is_season_registration != 'yes'"/>
                                    <field name="season_end_date" invisible="is_season_registration != 'yes'"/>
                                    <field name="first_registration_date"/>
                                    <field name="owner_registration_date"/>
                                    <field name="current_mileage"/>
                                    <field name="vehicle_value" widget="monetary"/>
                                    <field name="has_vat_deduction"/>
                                    <field name="payment_type"/>
                                </group>
                                <group>
                                    <field name="is_vehicle_at_owner_address"/>
                                </group>
                            </group>
                            <group invisible="is_vehicle_at_owner_address != 'no'">
                                <group string="Fahrzeugstandort">
                                    <field name="vehicle_street"/>
                                    <field name="vehicle_street_number"/>
                                    <field name="vehicle_street_addition"/>
                                    <field name="vehicle_zip"/>
                                    <field name="vehicle_city"/>
                                    <field name="vehicle_country_id"/>
                                </group>
                            </group>
                        </page>
                        <page string="Fahrer">
                            <group>
                                <group>
                                    <field name="driver1_name"/>
                                    <field name="driver1_birthdate"/>
                                    <field name="has_additional_driver"/>
                                </group>
                            </group>
                            <group invisible="has_additional_driver != 'yes'">
                                <group string="Fahrer 2">
                                    <field name="driver2_name"/>
                                    <field name="driver2_birthdate"/>
                                    <field name="has_driver3"/>
                                </group>
                            </group>
                            <group invisible="has_additional_driver != 'yes' or has_driver3 != 'yes'">
                                <group string="Fahrer 3">
                                    <field name="driver3_name"/>
                                    <field name="driver3_birthdate"/>
                                    <field name="has_driver4"/>
                                </group>
                            </group>
                            <group invisible="has_additional_driver != 'yes' or has_driver3 != 'yes' or has_driver4 != 'yes'">
                                <group string="Fahrer 4">
                                    <field name="driver4_name"/>
                                    <field name="driver4_birthdate"/>
                                </group>
                            </group>
                        </page>
                        <page string="Nutzung &amp; Risiko">
                            <group>
                                <group>
                                    <field name="has_similar_vehicles" string="Besitzen Sie weitere vergleichbare Fahrzeuge?"/>
                                    <field name="similar_vehicles_description" invisible="has_similar_vehicles != 'yes'"/>
                                    <field name="has_previous_experience" string="Haben Sie Vorerfahrung (Fahrerfahrung) auf vergleichbaren Fahrzeugen?"/>
                                    <field name="has_previous_damage" invisible="1"/>
                                    <field name="liability_damage_count" invisible="has_previous_damage != 'yes'"/>
                                    <field name="full_coverage_damage_count" invisible="has_previous_damage != 'yes'"/>
                                    <field name="partial_coverage_damage_count" invisible="has_previous_damage != 'yes'"/>
                                </group>
                                <group>
                                    <field name="daily_vehicle_license"/>
                                    <field name="license_plate_text"/>
                                    <field name="risk_requirement"/>
                                    <field name="has_burglar_alarm"/>
                                    <field name="has_alarm_police_connection" invisible="has_burglar_alarm != 'yes'"/>
                                    <field name="has_fire_alarm"/>
                                    <field name="has_fire_department_connection" invisible="has_fire_alarm != 'yes'"/>
                                </group>
                            </group>
                        </page>
                        <page string="Vergleichbare Fahrzeuge">
                            <group>
                                <group>
                                    <field name="vf_usage_purpose"/>
                                    <field name="vf_annual_mileage"/>
                                    <field name="vf_has_tracker"/>
                                    <field name="vf_has_previous_insurance"/>
                                    <field name="vf_previous_insurance_details" invisible="vf_has_previous_insurance != 'yes'"/>
                                    <field name="vf_has_previous_damage" string="Sind Vorschäden im Bereich KFZ vorhanden?"/>
                                    <field name="vf_liability_damage_count" invisible="vf_has_previous_damage != 'yes'"/>
                                    <field name="vf_full_coverage_damage_count" invisible="vf_has_previous_damage != 'yes'"/>
                                    <field name="vf_partial_coverage_damage_count" invisible="vf_has_previous_damage != 'yes'"/>
                                </group>
                            </group>
                        </page>
                        <page string="Dokumente &amp; Bemerkungen">
                            <group>
                                <group>
                                    <field name="value_proof" filename="value_proof_filename"/>
                                    <field name="value_proof_filename" invisible="1"/>
                                    <field name="vehicle_documents" filename="vehicle_documents_filename"/>
                                    <field name="vehicle_documents_filename" invisible="1"/>
                                    <field name="photos" filename="photos_filename"/>
                                    <field name="photos_filename" invisible="1"/>
                                </group>
                                <group>
                                    <field name="notes"/>
                                    <field name="notizen"/>
                                    <field name="internal_notes"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_premiumcar_erstanfrage_endkunden_intern_tree" model="ir.ui.view">
        <field name="name">premiumcar.erstanfrage.endkunden.intern.tree</field>
        <field name="model">premiumcar.erstanfrage.endkunden.intern</field>
        <field name="arch" type="xml">
            <tree string="PremiumCar Erstanfrage Endkunden Intern">
                <field name="name"/>
                <field name="first_name"/>
                <field name="last_name"/>
                <field name="car_manufacturer_id"/>
                <field name="car_type"/>
                <field name="insurance_start_date"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="state"/>
                <field name="create_date"/>
                <field name="lead_id"/>
                <field name="partner_id"/>
            </tree>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_premiumcar_erstanfrage_endkunden_intern_search" model="ir.ui.view">
        <field name="name">premiumcar.erstanfrage.endkunden.intern.search</field>
        <field name="model">premiumcar.erstanfrage.endkunden.intern</field>
        <field name="arch" type="xml">
            <search string="PremiumCar Erstanfrage Endkunden Intern">
                <field name="name"/>
                <field name="first_name"/>
                <field name="last_name"/>
                <field name="email"/>
                <field name="car_manufacturer_id"/>
                <field name="car_type"/>
                <field name="partner_id"/>
                <separator/>
                <filter string="Entwurf" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Eingereicht" name="submitted" domain="[('state', '=', 'submitted')]"/>
                <filter string="Bearbeitet" name="processed" domain="[('state', '=', 'processed')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="status" context="{'group_by': 'state'}"/>
                    <filter string="Versicherungsart" name="group_by_versicherungsart" domain="[]" context="{'group_by':'usage_purpose'}"/>
                    <filter string="Fahrzeughersteller" name="group_by_fahrzeughersteller" domain="[]" context="{'group_by':'car_manufacturer_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_premiumcar_erstanfrage_endkunden_intern" model="ir.actions.act_window">
        <field name="name">PremiumCar Erstanfrage Endkunden Intern</field>
        <field name="res_model">premiumcar.erstanfrage.endkunden.intern</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('company_id', '=', company_id)]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Erstellen Sie eine neue PremiumCar Erstanfrage Endkunden Intern
            </p>
        </field>
    </record>

    <!-- Menu -->
    <menuitem id="menu_premiumcar_erstanfrage_endkunden_intern"
              name="Premium Car Erstanfrage Endkunden - Intern"
              action="action_premiumcar_erstanfrage_endkunden_intern"
              parent="menu_premium_car_request_root"
              sequence="40"/>
</odoo>