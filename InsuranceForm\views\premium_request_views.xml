<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_premium_car_request_form" model="ir.ui.view">
        <field name="name">premium.car.request.form</field>
        <field name="model">premium.car.request</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <field name="name" placeholder="Subject"/>
                        <field name="company_id" groups="base.group_multi_company"/>
                    </group>
                    <group string="Broker Information">
                        <field name="salutation"/>
                        <field name="first_name"/>
                        <field name="last_name"/>
                        <field name="street_number"/>
                        <field name="street_addition"/>
                        <field name="zip"/>
                        <field name="city"/>
                        <field name="country_id"/>
                        <field name="phone"/>
                        <field name="email"/>
                        <field name="has_customer_authority"/>
                    </group>

                    <group string="Vehicle Information">
                        <group>
                            <field name="vehicle_manufacturer"/>
                            <field name="vehicle_type"/>
                            <field name="vehicle_value"/>
                            <field name="license_plate"/>
                            <field name="first_registration"/>
                            <field name="registration_on_policyholder"/>
                            <field name="current_milage"/>
                            <field name="annual_milage"/>
                        </group>
                    </group>

                    <group string="Insurance Information">
                        <group>
                            <field name="insurance_start_date"/>
                            <field name="seasonal_registration"/>
                            <field name="seasonal_period_from" invisible="not seasonal_registration"/>
                            <field name="seasonal_period_to" invisible="not seasonal_registration"/>
                            <field name="input_tax_deduction_entitlement"/>
                            <field name="payment_type_for_vehicle"/>
                            <field name="usage_purpose"/>
                            <field name="existing_insurance"/>
                            <field name="previous_insurer_and_policy_number"/>
                            <field name="previous_claims"/>
                        </group>
                    </group>

                    <group string="Driver Information">
                        <group>
                            <field name="driver_1"/>
                            <field name="dob_driver_1"/>
                            <field name="driver_2"/>
                            <field name="dob_driver_2"/>
                            <field name="driver_3"/>
                            <field name="dob_driver_3"/>
                            <field name="driver_4"/>
                            <field name="dob_driver_4"/>
                        </group>
                    </group>

                    <group string="Security &amp; Risk Information">
                        <group>
                            <field name="risk_requirement"/>
                            <field name="tracker"/>
                            <field name="burglary_alarm_system"/>
                            <field name="fire_alarm_system"/>
                        </group>
                    </group>

                    <group string="Vehicle Owner Information">
                        <group>
                            <field name="divergent_vehicle_owner"/>
                            <field name="owning_comparable_vehicles"/>
                            <field name="driving_experience_with_comparable_vehicle"/>
                            <field name="main_vehicle_location_at_policyholder_address"/>
                            <field name="license_plate_of_daily_use_vehicle"/>
                        </group>
                    </group>

                    <group string="Additional Information">
                        <field name="other_information" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Tree View -->
    <record id="view_premium_car_request_tree" model="ir.ui.view">
        <field name="name">premium.car.request.tree</field>
        <field name="model">premium.car.request</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="first_name"/>
                <field name="last_name"/>
                <field name="phone"/>
                <field name="vehicle_manufacturer"/>
                <field name="vehicle_type"/>
                <field name="insurance_start_date"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="create_date"/>
            </tree>
        </field>
    </record>

    <!-- Action -->
    <record id="action_premium_car_request" model="ir.actions.act_window">
        <field name="name">PremiumCar Erstanfrage Untervermittler</field>
        <field name="res_model">premium.car.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('company_id', '=', company_id)]</field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_premium_car_request_root" name="Premium Car Insurance" sequence="10"/>
    <menuitem id="menu_premium_car_request" parent="menu_premium_car_request_root" name="PremiumCar Erstanfrage Untervermittler" action="action_premium_car_request" sequence="10"/>

    <!-- Website Menu - Hidden to avoid duplication -->
    <record id="menu_website_premium_car" model="website.menu">
        <field name="name">PremiumCar Erstanfrage Untervermittler</field>
        <field name="url">/premiumcar-erstanfrage-untervermittler</field>
        <field name="parent_id" ref="website.main_menu"/>
        <field name="sequence" type="int">50</field>
        <field name="is_visible">False</field>
    </record>

    <!-- Website Page for the main URL -->
    <record id="premium_car_page" model="website.page">
        <field name="url">/premiumcar-erstanfrage-untervermittler</field>
        <field name="website_published">True</field>
        <field name="view_id" ref="InsuranceForm.premium_car_form_template"/>
        <field name="name">PremiumCar Erstanfrage Untervermittler</field>
        <field name="is_published">True</field>
        <field name="type">qweb</field>
        <!-- This is the active website.page record that should be used -->
    </record>

    <!-- Website Page for the short URL -->
    <record id="premium_car_short_page" model="website.page">
        <field name="url">/premiumcar</field>
        <field name="website_published">True</field>
        <field name="view_id" ref="InsuranceForm.premium_car_form_template"/>
        <field name="name">PremiumCar Erstanfrage Untervermittler</field>
        <field name="is_published">True</field>
        <field name="type">qweb</field>
    </record>
</odoo>