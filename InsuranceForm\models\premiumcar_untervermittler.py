from odoo import models, fields, api, _
from odoo.exceptions import UserError
from datetime import date
import io
import base64
import traceback
import logging
import os

_logger = logging.getLogger(__name__)

# Check if ReportLab is available
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import BaseDocTemplate, PageTemplate, Frame, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.units import inch
    from reportlab.lib.enums import TA_CENTER
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    _logger.warning("ReportLab is not available. PDF generation will not work.")

_logger = logging.getLogger(__name__)

class PremiumCarUntervermittler(models.Model):
    _name = 'premiumcar.untervermittler'
    _description = 'PremiumCar Erstanfrage Untervermittler - Intern'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    # Selection field mappings for CRM lead
    SELECTION_MAPS = {
        'salutation': {
            'mr': 'mr',
            'mrs': 'mrs',
            'diverse': 'diverse',
            'company': 'company',
            'other': 'other',
        },
        'has_vat_deduction': {
            'yes': 'yes',
            'no': 'no',
        },
        'payment_type': {
            'leasing': 'leasing',
            'financing': 'financing',
            'cash': 'cash_payment',
        },
        'usage_purpose': {
            'mostly_private': 'only_private',
            'mostly_business': 'only_professional',
            'exclusively_private': 'only_private',
            'exclusively_business': 'only_professional',
            'private': 'only_private',
            'business': 'only_professional',
            'mixed': 'mainly_private',
            'only_private': 'only_private',
            'only_business': 'only_professional',
        },
        'has_tracker': {
            'yes': 'yes',
            'no': 'no',
        },
        'has_previous_insurance': {
            'yes': 'yes',
            'no': 'no',
        },
        'has_previous_damage': {
            'yes': 'yes',
            'no': 'no',
        },
        'has_similar_vehicles': {
            'yes': 'yes',
            'no': 'no',
        },
        'has_previous_experience': {
            'yes': 'yes',
            'no': 'no',
        },
        'is_vehicle_at_owner_address': {
            'yes': 'yes',
            'no': 'no',
        },
        'risk_requirement': {
            'single_garage': 'singe_or_double_garage',  # Note: This matches the typo in the CRM lead model
            'communal_garage': 'collective_garage_or_hall',
            'no_garage': 'no_lockable_parking_space',
        },
        'has_burglar_alarm': {
            'yes': 'yes',
            'no': 'no',
        },
        'has_fire_alarm': {
            'yes': 'yes',
            'no': 'no',
        },
        'is_owner_different': {
            'yes': 'yes',
            'no': 'no',
        },
    }

    name = fields.Char(string="Request Reference", required=True, copy=False, readonly=True,
                       default=lambda self: 'New')

    # Broker Information
    broker_salutation = fields.Selection([
        ('mr', 'Herr'),
        ('mrs', 'Frau'),
        ('diverse', 'Divers'),
        ('company', 'Firma'),
        ('other', 'Sonstige')
    ], string='Anrede', required=True)
    broker_first_name = fields.Char('Vorname', required=True)
    broker_last_name = fields.Char('Nachname', required=True)
    broker_street = fields.Char('Straße', required=True)
    broker_street_number = fields.Char('Nr.', required=True)
    broker_street_addition = fields.Char('Adresszusatz', required=True)
    broker_zip = fields.Char('PLZ', required=True)
    broker_city = fields.Char('Ort', required=True)
    broker_country_id = fields.Many2one('res.country', string='Land', required=True,
                                 default=lambda self: self.env['res.country'].search([('code', '=', 'DE')], limit=1).id)
    broker_email = fields.Char('E-Mail')
    broker_phone = fields.Char('Telefonnummer')

    # Customer Authorization
    has_customer_authorization = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Können Sie uns bestätigen, dass Ihnen eine aktuelle/unterschriebene Vollmacht des Kunden vorliegt?', required=True)

    # Insurance Start Date
    insurance_start_date = fields.Date('Versicherungsbeginn', required=True)

    # Policyholder Information
    salutation = fields.Selection([
        ('mr', 'Herr'),
        ('mrs', 'Frau'),
        ('diverse', 'Divers'),
        ('company', 'Firma'),
        ('other', 'Sonstige')
    ], string='Anrede', required=True)
    first_name = fields.Char('Vorname', required=True)
    last_name = fields.Char('Nachname', required=True)
    policy_holder_dob = fields.Date('Geburtsdatum', required=True)
    street = fields.Char('Straße', required=True)
    street_number = fields.Char('Nr.', required=True)
    street_addition = fields.Char('Adresszusatz', required=True)
    zip = fields.Char('PLZ', required=True)
    city = fields.Char('Ort', required=True)
    country_id = fields.Many2one('res.country', string='Land', required=True,
                                 default=lambda self: self.env['res.country'].search([('code', '=', 'DE')], limit=1).id)
    email = fields.Char('E-Mail')
    phone = fields.Char('Telefonnummer')
    mobile = fields.Char('Mobilfunknummer')

    # Owner different from policyholder
    is_owner_different = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Halter abweichend vom Versicherungsnehmer?', required=True)

    # Owner Information when different
    owner_salutation = fields.Selection([
        ('mr', 'Herr'),
        ('mrs', 'Frau'),
        ('diverse', 'Divers'),
        ('company', 'Firma'),
        ('other', 'Sonstige')
    ], string='Anrede (Halter)')
    owner_first_name = fields.Char('Vorname (Halter)')
    owner_last_name = fields.Char('Nachname (Halter)')
    owner_street = fields.Char('Straße (Halter)')
    owner_street_number = fields.Char('Nr. (Halter)')
    owner_street_addition = fields.Char('Adresszusatz (Halter)')
    owner_zip = fields.Char('PLZ (Halter)')
    owner_city = fields.Char('Ort (Halter)')
    owner_country_id = fields.Many2one('res.country', string='Land (Halter)',
                                      default=lambda self: self.env['res.country'].search([('code', '=', 'DE')], limit=1).id)
    owner_email = fields.Char('E-Mail (Halter)')
    owner_phone = fields.Char('Telefonnummer (Halter)')
    owner_mobile = fields.Char('Mobilfunknummer (Halter)')

    # Vehicle Information
    car_manufacturer_id = fields.Many2one('vehicle.manufacturer', string='Fahrzeughersteller', required=True)
    car_type = fields.Char('Fahrzeugtyp', required=True)
    license_plate = fields.Char('Amtliches Kennzeichen')

    # Season Registration
    is_season_registration = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Wird das Fahrzeug auf Saison zugelassen?', required=True)
    season_start_date = fields.Date(string='Saisonzeitraum Start')
    season_end_date = fields.Date(string='Saisonzeitraum Ende')

    # Registration dates
    first_registration_date = fields.Date('Erstzulassungsdatum', required=True)
    owner_registration_date = fields.Date('Zulassungsdatum auf den Versicherungsnehmer', required=True)

    # Vehicle condition and value
    current_mileage = fields.Integer('Aktueller km-Stand', required=True)
    vehicle_value = fields.Float('Fahrzeugwert Brutto', required=True)

    # VAT deduction
    has_vat_deduction = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besteht Vorsteuerabzugsberechtigung?', required=True)

    # Payment type
    payment_type = fields.Selection([
        ('leasing', 'Leasing'),
        ('financing', 'Finanzierung'),
        ('cash', 'Barzahlung')
    ], string='Zahlart des Fahrzeuges', required=True)

    # Driver information
    driver1_name = fields.Char('Vor- und Nachname Fahrer 1', required=True)
    driver1_birthdate = fields.Date('Geburtsdatum Fahrer 1', required=True)

    # Additional driver
    has_additional_driver = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Möchten Sie einen weiteren Fahrer benennen?', required=True)

    # Additional Driver Information
    driver2_name = fields.Char('Vor- und Nachname Fahrer 2')
    driver2_birthdate = fields.Date('Geburtsdatum Fahrer 2')
    has_driver3 = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Weiterer Fahrer 3')
    driver3_name = fields.Char('Vor- und Nachname Fahrer 3')
    driver3_birthdate = fields.Date('Geburtsdatum Fahrer 3')
    has_driver4 = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Weiterer Fahrer 4')
    driver4_name = fields.Char('Vor- und Nachname Fahrer 4')
    driver4_birthdate = fields.Date('Geburtsdatum Fahrer 4')

    # Vehicle usage
    usage_purpose = fields.Selection([
        ('private', 'Privat'),
        ('business', 'Geschäftlich'),
        ('mixed', 'Gemischt'),
        ('mostly_private', 'überwiegend privat'),
        ('mostly_business', 'überwiegend beruflich'),
        ('exclusively_private', 'ausschließlich privat'),
        ('exclusively_business', 'ausschließlich beruflich')
    ], string='Verwendungszweck des Fahrzeuges', required=True)

    # Annual mileage
    annual_mileage = fields.Integer('Jahresfahrleistung', required=True)

    # Security features
    has_tracker = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Tracker vorhanden?', required=True)

    # Previous insurance
    has_previous_insurance = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Vorversicherung vorhanden?', required=True)

    previous_insurance_details = fields.Text(string='Versicherer und Versicherungsscheinnummer der Vorversicherung', tracking=True)

    # Previous damage
    has_previous_damage = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Vorschäden im Bereich KFZ vorhanden?', required=True)

    previous_damage_details = fields.Text(string='Bitte beschreiben Sie die Vorschäden', tracking=True)

    # Similar vehicles
    has_similar_vehicles = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besitzt Ihr Kunde weitere vergleichbare Fahrzeuge?', required=True)

    similar_vehicles_description = fields.Text(string='Beschreibung der vergleichbaren Fahrzeuge', tracking=True)

    # Previous driving experience
    has_previous_experience = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Hat Ihr Kunde Vorerfahrung (Fahrerfahrung) auf vergleichbaren Fahrzeugen?', required=True)

    # Daily vehicle license plate
    daily_vehicle_license = fields.Char('Kennzeichen des Alltagsfahrzeuges')

    # Vehicle location
    is_vehicle_at_owner_address = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Überwiegender Fahrzeugstandort unter der Anschrift des Versicherungsnehmers?', required=True)

    # Vehicle location fields
    vehicle_street = fields.Char(string='Straße (Fahrzeugstandort)')
    vehicle_street_number = fields.Char(string='Nr. (Fahrzeugstandort)')
    vehicle_street_addition = fields.Char(string='Adresszusatz (Fahrzeugstandort)')
    vehicle_zip = fields.Char(string='PLZ (Fahrzeugstandort)')
    vehicle_city = fields.Char(string='Ort (Fahrzeugstandort)')
    vehicle_country_id = fields.Many2one('res.country', string='Land (Fahrzeugstandort)',
                                       default=lambda self: self.env['res.country'].search([('code', '=', 'DE')], limit=1).id)

    # Risk requirements
    risk_requirement = fields.Selection([
        ('single_garage', 'Einzel-/Doppelgarage'),
        ('communal_garage', 'Sammelgarage/Halle'),
        ('no_garage', 'keine abschließbare, überdachte und durch feste Wände umschlossene Abstellmöglichkeit')
    ], string='Risikoannahmevoraussetzung für das Fahrzeug', required=True)

    # Security systems
    has_burglar_alarm = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Ist eine Einbruchmeldeanlage vorhanden?', required=True)

    has_alarm_police_connection = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besteht eine Aufschaltung (z.B. zur Polizei)', tracking=True)

    has_fire_alarm = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Ist eine Brandmeldeanlage (VdS anerkannt) vorhanden?', required=True)

    has_fire_department_connection = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besteht eine Aufschaltung zur Feuerwehr', tracking=True)

    # Documents
    value_proof = fields.Binary('Anschaffungsbeleg/Leasingvertrag bzw. die Fahrzeugbestellung', attachment=True)
    value_proof_filename = fields.Char('Anschaffungsbeleg Dateiname')

    vehicle_documents = fields.Binary('Fahrzeugunterlagen (Fahrzeugschein oder Brief)', attachment=True)
    vehicle_documents_filename = fields.Char('Fahrzeugunterlagen Dateiname')

    # Photos
    photos = fields.Binary('Fotos', attachment=True)
    photos_filename = fields.Char('Fotos Dateiname')

    # Additional notes
    notes = fields.Text('Möchten Sie uns noch etwas zu Ihrer Anfrage mitteilen?')
    internal_notes = fields.Text('Notizen')

    # Status
    state = fields.Selection([
        ('draft', 'Entwurf'),
        ('submitted', 'Eingereicht'),
        ('processed', 'Bearbeitet')
    ], default='draft', string='Status', tracking=True)

    # Link to CRM Lead
    lead_id = fields.Many2one('crm.lead', string='Related Lead', ondelete='set null')

    # Link to Contact
    partner_id = fields.Many2one('res.partner', string='Related Contact', ondelete='set null')

    # Company field for multi-company access control
    company_id = fields.Many2one('res.company', string='Company',
                                default=lambda self: self.env.company,
                                required=True,
                                help="Company that this request belongs to")

    # SQL constraints
    _sql_constraints = [
        ('company_id_required', 'CHECK (company_id IS NOT NULL)',
         'Company is required for all insurance requests.'),
    ]

    def translate_selections(self, vals):
        """Translate selection values from form to model"""
        # Create a copy of vals to avoid modifying the original
        translated_vals = vals.copy()

        # Don't apply payment_type mapping when creating the form record
        # Only apply mappings for CRM lead creation
        # This prevents 'cash' from being incorrectly mapped to 'cash_payment' in the form record
        excluded_fields = ['risk_requirement', 'usage_purpose', 'payment_type']
        for field, mapping in self.SELECTION_MAPS.items():
            if field in translated_vals and translated_vals[field] in mapping and field not in excluded_fields:
                translated_vals[field] = mapping[translated_vals[field]]

        return translated_vals

    @api.model_create_multi
    def create(self, vals_list):
        new_vals_list = []
        for vals in vals_list:
            # Translate selection values
            vals = self.translate_selections(vals)
            new_vals_list.append(vals)

            if vals.get('name', 'New') == 'New':
                vals['name'] = self.env['ir.sequence'].next_by_code('premiumcar.untervermittler') or 'New'

            # Create a contact record for this customer
            # Generate a unique reference code
            timestamp = fields.Datetime.now().strftime('%Y%m%d%H%M%S')
            reference_code = f"PCU-{timestamp}"

            # Use clean name without timestamp
            clean_name = f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip()

            # Store the reference code for later use with the CRM lead
            vals['reference_code'] = reference_code

            partner_vals = {
                'name': clean_name,
                'ref': reference_code, # Store the reference code in the standard reference field
                'street': vals.get('street', ''),
                'street2': vals.get('street_addition', ''),
                'zip': vals.get('zip', ''),
                'city': vals.get('city', ''),
                'country_id': vals.get('country_id'),
                'email': vals.get('email', ''),
                'phone': vals.get('phone', ''),
                'type': 'contact',
                'company_type': 'person',
                # 'customer_rank': 1,  # Removed - not available in this Odoo version
                'comment': f"Created from PremiumCar Erstanfrage Untervermittler - Intern form {vals.get('name', 'New')}",
                'function': 'Fahrzeugversicherung',  # Add job position
            }

            # Set the proper title based on salutation
            if vals.get('salutation') == 'mr':
                title = self.env.ref('base.res_partner_title_mister', False)
                if title:
                    partner_vals['title'] = title.id
            elif vals.get('salutation') == 'mrs':
                title = self.env.ref('base.res_partner_title_madam', False)
                if title:
                    partner_vals['title'] = title.id

            # Define mail context to disable auto-followers
            MailCtx = {
                'mail_create_nolog': True,
                'mail_notrack': True,
                'tracking_disable': True,
                'mail_auto_subscription': False,
                'disable_duplicate_check': True,
            }

            # Create the partner with a savepoint to ensure transaction integrity
            with self.env.cr.savepoint():
                partner = self.env['res.partner'].sudo().with_context(**MailCtx).create(partner_vals)
                vals['partner_id'] = partner.id

                # Log success
                import logging
                _logger = logging.getLogger(__name__)
                _logger.info("Successfully created contact with ID %s from PremiumCar Erstanfrage Untervermittler - Intern form", partner.id)

            # Create a CRM lead for this request with detailed information
            manufacturer_name = ''
            if vals.get('car_manufacturer_id'):
                manufacturer = self.env['vehicle.manufacturer'].browse(vals.get('car_manufacturer_id'))
                if manufacturer.exists():
                    manufacturer_name = manufacturer.name or ''

            # Use the new formatting method from CRM lead model
            try:
                # Check if the format_description method exists in the CRM lead model
                if hasattr(self.env['crm.lead'], 'format_description'):
                    # Prepare values for the description formatter
                    description_vals = {
                        'car_manufacturer_id': vals.get('car_manufacturer_id'),
                        'vehicle_manufacturer': manufacturer_name,
                        'car_type': vals.get('car_type', ''),
                        'license_plate': vals.get('license_plate', ''),
                        'first_registration_date': vals.get('first_registration_date', ''),
                        'owner_registration_date': vals.get('owner_registration_date', ''),
                        'vehicle_value': vals.get('vehicle_value', 0),
                        'current_mileage': vals.get('current_mileage', 0),
                        'annual_mileage': vals.get('annual_mileage', 0),
                        'is_season_registration': vals.get('is_season_registration', ''),
                        'season_start_date': vals.get('season_start_date', ''),
                        'season_end_date': vals.get('season_end_date', ''),
                        'insurance_start_date': vals.get('insurance_start_date', ''),
                        'usage_purpose': vals.get('usage_purpose', ''),
                        'payment_type': vals.get('payment_type', ''),
                        'has_vat_deduction': vals.get('has_vat_deduction', ''),
                        'has_previous_insurance': vals.get('has_previous_insurance', ''),
                        'has_previous_damage': vals.get('has_previous_damage', ''),
                        'driver1_name': vals.get('driver1_name', ''),
                        'driver1_birthdate': vals.get('driver1_birthdate', ''),
                        'has_additional_driver': vals.get('has_additional_driver', ''),
                        'driver2_name': vals.get('driver2_name', ''),
                        'driver2_birthdate': vals.get('driver2_birthdate', ''),
                        'has_previous_experience': vals.get('has_previous_experience', ''),
                        'has_similar_vehicles': vals.get('has_similar_vehicles', ''),
                        'daily_vehicle_license': vals.get('daily_vehicle_license', ''),
                        'is_owner_different': vals.get('is_owner_different', ''),
                        'is_vehicle_at_owner_address': vals.get('is_vehicle_at_owner_address', ''),
                        'risk_requirement': vals.get('risk_requirement', ''),
                        'has_tracker': vals.get('has_tracker', ''),
                        'has_burglar_alarm': vals.get('has_burglar_alarm', ''),
                        'has_alarm_police_connection': vals.get('has_alarm_police_connection', ''),
                        'has_fire_alarm': vals.get('has_fire_alarm', ''),
                        'has_fire_department_connection': vals.get('has_fire_department_connection', ''),
                        'notes': vals.get('notes', ''),
                    }

                    # Use the formatting method
                    description = self.env['crm.lead'].format_description(description_vals)

                    # Add broker information at the beginning
                    broker_info = f"""# PremiumCar Erstanfrage Untervermittler - Intern Anfrage

## Vermittler Informationen
- **Name:** {vals.get('broker_first_name', '')} {vals.get('broker_last_name', '')}
- **Email:** {vals.get('broker_email', '')}
- **Telefon:** {vals.get('broker_phone', '')}
- **Kundenmandat:** {vals.get('has_customer_authorization', '')}

"""

                    description = broker_info + description
                else:
                    # Fallback to basic formatting if the method doesn't exist
                    description = f"""
## PremiumCar Erstanfrage Untervermittler - Intern
- Name: {vals.get('broker_first_name', '')} {vals.get('broker_last_name', '')}
- Email: {vals.get('broker_email', '')}
- Phone: {vals.get('broker_phone', '')}
- Customer Authorization: {vals.get('has_customer_authorization', '')}

## Fahrzeug Informationen
- Hersteller: {manufacturer_name}
- Typ: {vals.get('car_type', '')}
- Kennzeichen: {vals.get('license_plate', '')}
- Erstzulassungsdatum: {vals.get('first_registration_date', '')}
- Fahrzeugwert: {vals.get('vehicle_value', 0)} €
- Kilometerstand: {vals.get('current_mileage', 0)} km
- Jahresfahrleistung: {vals.get('annual_mileage', 0)} km

## Versicherungsinformationen
- Versicherungsbeginn: {vals.get('insurance_start_date', '')}
- Verwendungszweck: {vals.get('usage_purpose', '')}
- Zahlart: {vals.get('payment_type', '')}
- Vorsteuerabzugsberechtigung: {vals.get('has_vat_deduction', '')}

## Risikoinformationen
- Tracker vorhanden: {vals.get('has_tracker', '')}
- Vorversicherung: {vals.get('has_previous_insurance', '')}
- Vorschäden: {vals.get('has_previous_damage', '')}
- Risikoannahmevoraussetzung: {vals.get('risk_requirement', '')}
- Einbruchmeldeanlage: {vals.get('has_burglar_alarm', '')}
- Brandmeldeanlage: {vals.get('has_fire_alarm', '')}

## Fahrer Informationen
- Fahrer 1: {vals.get('driver1_name', '')}
- Geburtsdatum: {vals.get('driver1_birthdate', '')}
- Weitere Fahrer: {vals.get('has_additional_driver', '')}

## Zusätzliche Informationen
{vals.get('notes', '')}
"""
            except Exception as e:
                # Log the error but don't fail the creation process
                import logging
                _logger = logging.getLogger(__name__)
                _logger.error("Error formatting description: %s", str(e))

                # Fallback to basic formatting
                description = f"""
## PremiumCar Erstanfrage Untervermittler - Intern
- Name: {vals.get('broker_first_name', '')} {vals.get('broker_last_name', '')}
- Email: {vals.get('broker_email', '')}
- Phone: {vals.get('broker_phone', '')}
- Customer Authorization: {vals.get('has_customer_authorization', '')}

## Fahrzeug Informationen
- Hersteller: {manufacturer_name}
- Typ: {vals.get('car_type', '')}
- Kennzeichen: {vals.get('license_plate', '')}
- Erstzulassungsdatum: {vals.get('first_registration_date', '')}
- Fahrzeugwert: {vals.get('vehicle_value', 0)} €
- Kilometerstand: {vals.get('current_mileage', 0)} km
- Jahresfahrleistung: {vals.get('annual_mileage', 0)} km

## Versicherungsinformationen
- Versicherungsbeginn: {vals.get('insurance_start_date', '')}
- Verwendungszweck: {vals.get('usage_purpose', '')}
- Zahlart: {vals.get('payment_type', '')}
- Vorsteuerabzugsberechtigung: {vals.get('has_vat_deduction', '')}

## Risikoinformationen
- Tracker vorhanden: {vals.get('has_tracker', '')}
- Vorversicherung: {vals.get('has_previous_insurance', '')}
- Vorschäden: {vals.get('has_previous_damage', '')}
- Risikoannahmevoraussetzung: {vals.get('risk_requirement', '')}
- Einbruchmeldeanlage: {vals.get('has_burglar_alarm', '')}
- Brandmeldeanlage: {vals.get('has_fire_alarm', '')}

## Fahrer Informationen
- Fahrer 1: {vals.get('driver1_name', '')}
- Geburtsdatum: {vals.get('driver1_birthdate', '')}
- Weitere Fahrer: {vals.get('has_additional_driver', '')}

## Zusätzliche Informationen
{vals.get('notes', '')}
"""

            # Use the opportunity mapper to map form fields to opportunity fields
            lead_vals = self.env['opportunity.mapper'].map_form_to_opportunity(vals, 'premium_car')

            # Ensure basic lead information is set
            if not lead_vals.get('name'):
                lead_vals['name'] = f"PremiumCar Erstanfrage Untervermittler - Intern - {vals.get('first_name', '')} {vals.get('last_name', '')}"
            if not lead_vals.get('type'):
                lead_vals['type'] = 'lead'  # Create as lead, not opportunity
            if not lead_vals.get('phone') and vals.get('phone'):
                lead_vals['phone'] = vals.get('phone')
            if not lead_vals.get('email_from') and vals.get('email'):
                lead_vals['email_from'] = vals.get('email')
            if not lead_vals.get('contact_name'):
                lead_vals['contact_name'] = f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip()
            if not lead_vals.get('street'):
                lead_vals['street'] = (vals.get('street', '') or '') + (' ' + (vals.get('street_number', '') or '') if vals.get('street_number') else '')
            if not lead_vals.get('street2'):
                lead_vals['street2'] = vals.get('street_addition', '')
            if not lead_vals.get('zip'):
                lead_vals['zip'] = vals.get('zip', '')
            if not lead_vals.get('city'):
                lead_vals['city'] = vals.get('city', '')
            if not lead_vals.get('country_id'):
                lead_vals['country_id'] = vals.get('country_id')
            if not lead_vals.get('description'):
                lead_vals['description'] = description
            if not lead_vals.get('function'):
                lead_vals['function'] = 'Fahrzeugversicherung'

            # Add broker information
            lead_vals['broker_name'] = f"{vals.get('broker_first_name', '')} {vals.get('broker_last_name', '')}"
            lead_vals['broker_email'] = vals.get('broker_email', '')
            lead_vals['broker_phone'] = vals.get('broker_phone', '')
            if 'signed_mandate' in self.env['crm.lead'].sudo().fields_get():
                lead_vals['signed_mandate'] = vals.get('has_customer_authorization') == 'yes'

            # Safely get the list of available premium car fields from the CRM lead model
            # This ensures we only add fields that actually exist
            crm_model = self.env['crm.lead'].sudo()
            available_fields = []

            # Try to use the get_premium_car_fields method if it exists
            if hasattr(crm_model, 'get_premium_car_fields'):
                try:
                    available_fields = crm_model.get_premium_car_fields()
                except Exception as e:
                    import logging
                    _logger = logging.getLogger(__name__)
                    _logger.warning("Error calling get_premium_car_fields: %s", str(e))

            # If get_premium_car_fields doesn't exist or failed, use our filter_valid_fields function
            if not available_fields and hasattr(self.env['opportunity.mapper'], 'filter_valid_fields'):
                try:
                    # Get all opportunity fields first
                    opp_fields = self.env['crm.lead'].fields_get()
                    # Create a dict with potential premium car fields and filter them
                    premium_car_fields = {
                        'vehicle_manufacturer': True,
                        'vehicle_type': True,
                        'vehicle_value': True,
                        'insurance_start_date': True,
                        'license_plate': True,
                        'first_registration': True,
                        'current_milage': True,
                        'annual_milage': True,
                        'driver_1': True,
                        'dob_driver_1': True,
                        'driver_2': True,
                        'dob_driver_2': True,
                        'driver_3': True,
                        'dob_driver_3': True,
                        'driver_4': True,
                        'dob_driver_4': True,
                        'burglary_alarm_system': True,
                        'fire_alarm_system': True,
                        'tracker': True,
                        'existing_insurance': True,
                        'previous_claims': True,
                        'risk_acceptance_condition': True,
                        'policy_holder_dob': True,
                    }
                    available_fields = list(self.env['opportunity.mapper'].filter_valid_fields(premium_car_fields).keys())
                except Exception as e:
                    import logging
                    _logger = logging.getLogger(__name__)
                    _logger.warning("Error filtering valid fields: %s", str(e))
                    # Fall back to empty list, which will just use the mapped fields

            # Add extended fields only if they exist in the CRM model
            extended_fields = {
                # Set policy category to 'vehicle' for Premium Car
                'policy_category': 'vehicle',
                'category': 'vehicle',  # Map to CRM field
                'insurance_category_id': self.env['insurance.category'].sudo().search([('name', '=', 'Vehicle Insurance')], limit=1).id,

                # Vehicle Information
                'vehicle_manufacturer': vals.get('car_manufacturer_id', False),  # Now a Many2one field
                'vehicle_type': vals.get('car_type', ''),
                'license_plate': vals.get('license_plate', ''),
                'is_season_registration': vals.get('is_season_registration', 'no'),
                'first_registration_date': vals.get('first_registration_date', False),
                'owner_registration_date': vals.get('owner_registration_date', False),
                'current_mileage': vals.get('current_mileage', 0),
                'vehicle_value': vals.get('vehicle_value', 0),

                # Insurance Information
                'insurance_start_date': vals.get('insurance_start_date', False),
                'has_vat_deduction': vals.get('has_vat_deduction', 'no'),
                'payment_type': vals.get('payment_type', 'cash'),
                'usage_purpose': self.SELECTION_MAPS['usage_purpose'].get(vals.get('usage_purpose', 'private'), 'only_private'),
                'annual_mileage': vals.get('annual_mileage', 0),

                # Driver Information
                'driver1_name': vals.get('driver1_name', ''),
                'driver1_birthdate': vals.get('driver1_birthdate', False),
                'has_additional_driver': vals.get('has_additional_driver', 'no'),

                # Security features
                'has_tracker': vals.get('has_tracker', 'no'),

                # Previous insurance and damage
                'has_previous_insurance': vals.get('has_previous_insurance', 'no'),
                'has_previous_damage': vals.get('has_previous_damage', 'no'),

                # Similar vehicles
                'has_similar_vehicles': vals.get('has_similar_vehicles', 'no'),
                'has_previous_experience': vals.get('has_previous_experience', 'no'),
                'daily_vehicle_license': vals.get('daily_vehicle_license', ''),

                # Vehicle location
                'is_vehicle_at_owner_address': vals.get('is_vehicle_at_owner_address', 'yes'),

                # Risk requirements
                'risk_requirement': vals.get('risk_requirement', 'single_garage'),

                # Security systems
                'has_burglar_alarm': vals.get('has_burglar_alarm', 'no'),
                'has_alarm_police_connection': vals.get('has_alarm_police_connection', 'no'),
                'has_fire_alarm': vals.get('has_fire_alarm', 'no'),
                'has_fire_department_connection': vals.get('has_fire_department_connection', 'no'),

                # Owner different from policyholder
                'is_owner_different': vals.get('is_owner_different', 'no'),

                # Policyholder information
                'policy_holder_dob': vals.get('policy_holder_dob', False),  # Map policy holder birthdate directly
                'policy_holder_gender': 'male' if vals.get('salutation') == 'mr' else 'female' if vals.get('salutation') == 'mrs' else 'others',

                # Broker information
                'broker_name': f"{vals.get('broker_first_name', '')} {vals.get('broker_last_name', '')}",
                'broker_email': vals.get('broker_email', ''),
                'broker_phone': vals.get('broker_phone', ''),
                'signed_mandate': vals.get('has_customer_authorization') == 'yes',
            }

            # Only add fields that exist in the CRM lead model
            for field, value in extended_fields.items():
                if field in available_fields:
                    lead_vals[field] = value

            # Log which fields are available and which are not
            import logging
            _logger = logging.getLogger(__name__)
            _logger.info("Available premium car fields: %s", available_fields)
            _logger.info("Extended fields that exist: %s", [f for f in extended_fields if f in available_fields])
            _logger.info("Extended fields that don't exist: %s", [f for f in extended_fields if f not in available_fields])

            # Create the lead and link it to the contact if created
            if vals.get('partner_id'):
                lead_vals['partner_id'] = vals['partner_id']

            # Define mail context to disable auto-followers
            MailCtx = {
                'mail_create_nolog': True,
                'mail_notrack': True,
                'tracking_disable': True,
                'mail_auto_subscription': False,
            }

            # Create the lead with a savepoint
            with self.env.cr.savepoint():
                # Remove message_follower_ids if present
                lead_vals.pop('message_follower_ids', None)

                # Directly add policy_holder_dob to lead_vals, bypassing the filtering mechanism
                if vals.get('policy_holder_dob'):
                    lead_vals['policy_holder_dob'] = vals.get('policy_holder_dob')

                # Add reference code to website_ref_number field
                if vals.get('reference_code'):
                    lead_vals['website_ref_number'] = vals['reference_code']
                    # Also add to description for backward compatibility
                    if lead_vals.get('description'):
                        lead_vals['description'] = f"Reference Number: {vals['reference_code']}\n\n" + lead_vals['description']

                # Create the lead
                lead = self.env['crm.lead'].sudo().with_context(**MailCtx).create(lead_vals)
                vals['lead_id'] = lead.id

                # Set company_id from the created lead's policy provider
                if lead.policy_provider_id:
                    vals['company_id'] = lead.policy_provider_id.id
                    import logging
                    _logger = logging.getLogger(__name__)
                    _logger.info(f"Set company_id to {lead.policy_provider_id.name} from CRM lead")

                # Remove transient key so super().create() doesn't fail
                vals.pop('reference_code', None)

                # Ensure policy_holder_dob is set with a direct update
                if vals.get('policy_holder_dob'):
                    try:
                        lead.sudo().write({
                            'policy_holder_dob': vals.get('policy_holder_dob')
                        })
                    except Exception as e:
                        import logging
                        _logger = logging.getLogger(__name__)
                        _logger.error("Error updating policy_holder_dob: %s", str(e))

                # Subscribe partner manually (only once)
                if vals.get('partner_id'):
                    lead.message_subscribe(partner_ids=[vals['partner_id']])
                    _logger.info("Manually subscribed partner %s to lead %s", vals['partner_id'], lead.id)

                # Log the followers for debugging
                _logger.debug("LEAD FOLLOWERS AFTER CREATE: %r", lead.message_follower_ids.mapped('partner_id.id'))

        # Create the records
        records = super(PremiumCarUntervermittler, self).create(new_vals_list)

        # Update the CRM leads with the premium_car_form_id if the field exists
        for record in records:
            if record.lead_id:
                try:
                    # Check if the premium_car_form_id field exists in the CRM lead model
                    if 'premium_car_form_id' in self.env['crm.lead'].sudo().fields_get():
                        record.lead_id.sudo().write({
                            'premium_car_form_id': record.id
                        })
                except Exception as e:
                    # Log the error but don't fail the creation process
                    import logging
                    _logger = logging.getLogger(__name__)
                    _logger.error("Error updating CRM lead with premium_car_form_id: %s", str(e))

        return records

    def action_view_lead(self):
        """Open the CRM lead form view"""
        self.ensure_one()
        if not self.lead_id:
            return

        return {
            'name': 'CRM Lead',
            'type': 'ir.actions.act_window',
            'res_model': 'crm.lead',
            'res_id': self.lead_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def _get_logo_path(self):
        """Get the path to the company logo"""
        try:
            # Try multiple path resolution methods for Odoo
            possible_paths = []

            # Method 1: Standard module path
            module_path = os.path.dirname(os.path.dirname(__file__))
            possible_paths.append(os.path.join(module_path, 'static', 'img', 'focus_logo.png'))

            # Method 2: Using Odoo's get_module_path if available
            try:
                from odoo.modules import get_module_path
                module_path_odoo = get_module_path('InsuranceForm')
                if module_path_odoo:
                    possible_paths.append(os.path.join(module_path_odoo, 'static', 'img', 'focus_logo.png'))
            except:
                pass

            # Method 3: Relative to current working directory
            possible_paths.append(os.path.join('InsuranceForm', 'static', 'img', 'focus_logo.png'))

            _logger.info(f"Searching for logo in paths: {possible_paths}")

            # Try each path
            for logo_path in possible_paths:
                _logger.info(f"Checking logo path: {logo_path}")
                if os.path.exists(logo_path) and os.path.isfile(logo_path):
                    # Check if file has content and is not empty
                    if os.path.getsize(logo_path) > 0:
                        _logger.info(f"Logo file found and valid: {logo_path}")
                        return logo_path
                    else:
                        _logger.warning(f"Logo file exists but is empty: {logo_path}")
                else:
                    _logger.info(f"Logo file not found at: {logo_path}")

            _logger.warning("Logo file not found in any of the expected locations")
            return None

        except Exception as e:
            _logger.error(f"Error getting logo path: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _create_header_footer(self, canvas, doc):
        """Create header with logo and footer for PDF pages"""
        try:
            _logger.info("=== HEADER FUNCTION CALLED ===")

            # Get logo path
            logo_path = self._get_logo_path()

            if logo_path and os.path.exists(logo_path):
                _logger.info(f"Attempting to draw logo from: {logo_path}")

                # Draw logo in top-right corner with professional sizing
                # Position: 0.4 inch from right edge, 0.25 inch from top (reverted to original position)
                # Title will be positioned below the logo for better visual hierarchy
                logo_width = 2.0 * inch   # Increased from 1.2 to 2.0 inches (67% larger)
                logo_height = 1.0 * inch  # Increased from 0.6 to 1.0 inches (67% larger)

                x_position = A4[0] - 0.4*inch - logo_width  # Right edge minus padding minus logo width
                y_position = A4[1] - 0.25*inch - logo_height  # Top edge minus padding minus logo height (reverted to original)

                _logger.info(f"Drawing logo at position: x={x_position}, y={y_position}, width={logo_width}, height={logo_height}")

                canvas.drawImage(logo_path, x_position, y_position,
                               width=logo_width, height=logo_height,
                               preserveAspectRatio=True, mask='auto')

                _logger.info("Logo successfully drawn to PDF")
            else:
                _logger.info("No valid logo file found, skipping logo in PDF header")

        except Exception as e:
            # Log error but don't fail PDF generation
            _logger.error(f"Error adding logo to PDF: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            pass

    def generate_pdf_summary(self):
        """Generate a PDF summary of the Premium Car Erstanfrage Untervermittler Intern request"""
        self.ensure_one()

        if not REPORTLAB_AVAILABLE:
            _logger.error("ReportLab is not available. Cannot generate PDF.")
            raise UserError(_("PDF generation is not available. Please contact your administrator."))

        try:
            # Create a buffer to hold the PDF
            buffer = io.BytesIO()

            # Create the PDF document with custom page template
            doc = BaseDocTemplate(buffer, pagesize=A4, leftMargin=0.75*inch, rightMargin=0.75*inch)

            # Create a frame for the content
            frame = Frame(0.75*inch, 0.75*inch, A4[0] - 1.5*inch, A4[1] - 1.5*inch,
                         leftPadding=0, bottomPadding=0, rightPadding=0, topPadding=0)

            # Create page template with header/footer function
            page_template = PageTemplate(id='main', frames=[frame], onPage=self._create_header_footer)
            doc.addPageTemplates([page_template])

            story = []

            # Get styles
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                textColor=colors.darkred,
                alignment=1  # Center alignment
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                textColor=colors.darkblue
            )

            normal_style = styles['Normal']

            # Create a style for table labels to enable text wrapping
            label_style = ParagraphStyle(
                'LabelStyle',
                parent=styles['Normal'],
                fontSize=10,
                fontName='Helvetica-Bold',
                leading=12
            )

            value_style = ParagraphStyle(
                'ValueStyle',
                parent=styles['Normal'],
                fontSize=10,
                fontName='Helvetica',
                leading=12
            )

            # Add large spacer to push title below logo and improve page breaks
            story.append(Spacer(1, 80))

            # Title
            story.append(Paragraph("PremiumCar Erstanfrage Untervermittler-Intern - Zusammenfassung", title_style))
            story.append(Spacer(1, 20))

            # Reference number
            story.append(Paragraph(f"<b>Referenznummer:</b> {self.name}", normal_style))
            story.append(Spacer(1, 20))

            # Broker Information Section
            story.append(Paragraph("Daten des Vermittlers", heading_style))

            broker_data = [
                [Paragraph('Anrede:', label_style), Paragraph(self._get_broker_salutation_display(), value_style)],
                [Paragraph('Vorname:', label_style), Paragraph(self.broker_first_name or '', value_style)],
                [Paragraph('Nachname:', label_style), Paragraph(self.broker_last_name or '', value_style)],
                [Paragraph('E-Mail:', label_style), Paragraph(self.broker_email or '', value_style)],
                [Paragraph('Telefonnummer:', label_style), Paragraph(self._format_phone_number(self.broker_phone), value_style)],
            ]

            broker_table = Table(broker_data, colWidths=[3.2*inch, 3.3*inch])
            broker_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(broker_table)
            story.append(Spacer(1, 20))

            # Continue with more sections in the next part...
            return self._continue_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

        except Exception as e:
            _logger.error(f"Error generating PDF: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            raise UserError(_("Error generating PDF. Please try again or contact support."))

    def _continue_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Continue PDF generation with broker address and customer authorization sections"""

        # Broker Address Information
        broker_address_data = [
            [Paragraph('Straße:', label_style), Paragraph(f"{self.broker_street or ''} {self.broker_street_number or ''}".strip(), value_style)],
            [Paragraph('Adresszusatz:', label_style), Paragraph(self.broker_street_addition or '', value_style)],
            [Paragraph('PLZ:', label_style), Paragraph(self.broker_zip or '', value_style)],
            [Paragraph('Ort:', label_style), Paragraph(self.broker_city or '', value_style)],
            [Paragraph('Land:', label_style), Paragraph(self.broker_country_id.name if self.broker_country_id else '', value_style)],
        ]

        broker_address_table = Table(broker_address_data, colWidths=[3.2*inch, 3.3*inch])
        broker_address_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(broker_address_table)
        story.append(Spacer(1, 20))

        # Customer Authorization Section
        story.append(Paragraph("Kundenvollmacht", heading_style))

        auth_data = [
            [Paragraph('Unterschriebene Vollmacht vorliegend:', label_style), Paragraph(self._get_has_customer_authorization_display(), value_style)],
        ]

        auth_table = Table(auth_data, colWidths=[3.2*inch, 3.3*inch])
        auth_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(auth_table)
        story.append(Spacer(1, 20))

        # Insurance Information Section
        story.append(Paragraph("Versicherungsinformationen", heading_style))

        insurance_data = [
            [Paragraph('Versicherungsbeginn:', label_style), Paragraph(self.insurance_start_date.strftime('%d.%m.%Y') if self.insurance_start_date else '', value_style)],
        ]

        insurance_table = Table(insurance_data, colWidths=[3.2*inch, 3.3*inch])
        insurance_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(insurance_table)
        story.append(Spacer(1, 20))

        # Personal Information Section
        story.append(Paragraph("Daten des Versicherungsnehmers", heading_style))

        personal_data = [
            [Paragraph('Anrede:', label_style), Paragraph(self._get_salutation_display(), value_style)],
            [Paragraph('Vorname:', label_style), Paragraph(self.first_name or '', value_style)],
            [Paragraph('Nachname:', label_style), Paragraph(self.last_name or '', value_style)],
            [Paragraph('Geburtsdatum:', label_style), Paragraph(self.policy_holder_dob.strftime('%d.%m.%Y') if self.policy_holder_dob else '', value_style)],
        ]

        personal_table = Table(personal_data, colWidths=[3.2*inch, 3.3*inch])
        personal_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(personal_table)
        story.append(Spacer(1, 20))

        # Continue with address and contact information...
        return self._continue_address_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

    def _continue_address_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Continue PDF generation with address and contact information sections"""

        # Address Information Section
        story.append(Paragraph("Adresse", heading_style))

        address_data = [
            [Paragraph('Straße:', label_style), Paragraph(f"{self.street or ''} {self.street_number or ''}".strip(), value_style)],
            [Paragraph('Adresszusatz:', label_style), Paragraph(self.street_addition or '', value_style)],
            [Paragraph('PLZ:', label_style), Paragraph(self.zip or '', value_style)],
            [Paragraph('Ort:', label_style), Paragraph(self.city or '', value_style)],
            [Paragraph('Land:', label_style), Paragraph(self.country_id.name if self.country_id else '', value_style)],
        ]

        address_table = Table(address_data, colWidths=[3.2*inch, 3.3*inch])
        address_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(address_table)
        story.append(Spacer(1, 20))

        # Contact Information Section
        story.append(Paragraph("Kontaktdaten", heading_style))

        contact_data = [
            [Paragraph('E-Mail:', label_style), Paragraph(self.email or '', value_style)],
            [Paragraph('Telefonnummer:', label_style), Paragraph(self._format_phone_number(self.phone), value_style)],
            [Paragraph('Mobilfunknummer:', label_style), Paragraph(self._format_phone_number(self.mobile), value_style)],
        ]

        contact_table = Table(contact_data, colWidths=[3.2*inch, 3.3*inch])
        contact_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(contact_table)
        story.append(Spacer(1, 20))

        # Owner Information Section (only if different from policyholder)
        if self.is_owner_different == 'yes':
            story.append(Paragraph("Halter-Informationen", heading_style))

            owner_data = [
                [Paragraph('Anrede (Halter):', label_style), Paragraph(self._get_owner_salutation_display(), value_style)],
                [Paragraph('Vorname (Halter):', label_style), Paragraph(self.owner_first_name or '', value_style)],
                [Paragraph('Nachname (Halter):', label_style), Paragraph(self.owner_last_name or '', value_style)],
                [Paragraph('Straße (Halter):', label_style), Paragraph(f"{self.owner_street or ''} {self.owner_street_number or ''}".strip(), value_style)],
                [Paragraph('Adresszusatz (Halter):', label_style), Paragraph(self.owner_street_addition or '', value_style)],
                [Paragraph('PLZ (Halter):', label_style), Paragraph(self.owner_zip or '', value_style)],
                [Paragraph('Ort (Halter):', label_style), Paragraph(self.owner_city or '', value_style)],
                [Paragraph('Land (Halter):', label_style), Paragraph(self.owner_country_id.name if self.owner_country_id else '', value_style)],
                [Paragraph('E-Mail (Halter):', label_style), Paragraph(self.owner_email or '', value_style)],
                [Paragraph('Telefonnummer (Halter):', label_style), Paragraph(self._format_phone_number(self.owner_phone), value_style)],
                [Paragraph('Mobilfunknummer (Halter):', label_style), Paragraph(self._format_phone_number(self.owner_mobile), value_style)],
            ]

            owner_table = Table(owner_data, colWidths=[3.2*inch, 3.3*inch])
            owner_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(owner_table)
            story.append(Spacer(1, 20))

        # Continue with vehicle information...
        return self._continue_vehicle_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

    def _continue_vehicle_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Continue PDF generation with vehicle information sections"""

        # Vehicle Information Section
        story.append(Paragraph("Fahrzeuginformationen", heading_style))

        vehicle_data = [
            [Paragraph('Fahrzeughersteller:', label_style), Paragraph(self.car_manufacturer_id.name if self.car_manufacturer_id else '', value_style)],
            [Paragraph('Fahrzeugtyp:', label_style), Paragraph(self.car_type or '', value_style)],
            [Paragraph('Amtliches Kennzeichen:', label_style), Paragraph(self.license_plate or '', value_style)],
        ]

        vehicle_table = Table(vehicle_data, colWidths=[3.2*inch, 3.3*inch])
        vehicle_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(vehicle_table)
        story.append(Spacer(1, 20))

        # Registration Information Section
        story.append(Paragraph("Zulassungsinformationen", heading_style))

        registration_data = [
            [Paragraph('Saison-Zulassung:', label_style), Paragraph(self._get_is_season_registration_display(), value_style)],
            [Paragraph('Erstzulassungsdatum:', label_style), Paragraph(self.first_registration_date.strftime('%d.%m.%Y') if self.first_registration_date else '', value_style)],
            [Paragraph('Zulassungsdatum auf Versicherungsnehmer:', label_style), Paragraph(self.owner_registration_date.strftime('%d.%m.%Y') if self.owner_registration_date else '', value_style)],
        ]

        # Add seasonal period if applicable
        if self.is_season_registration == 'yes':
            if self.season_start_date:
                registration_data.append([Paragraph('Saisonzeitraum von:', label_style), Paragraph(self.season_start_date.strftime('%d.%m.%Y'), value_style)])
            if self.season_end_date:
                registration_data.append([Paragraph('Saisonzeitraum bis:', label_style), Paragraph(self.season_end_date.strftime('%d.%m.%Y'), value_style)])

        registration_table = Table(registration_data, colWidths=[3.2*inch, 3.3*inch])
        registration_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(registration_table)
        story.append(Spacer(1, 20))

        # Vehicle Value & Details Section
        story.append(Paragraph("Fahrzeugwert und Details", heading_style))

        value_data = [
            [Paragraph('Aktueller km-Stand:', label_style), Paragraph(f"{self.current_mileage:,}" if self.current_mileage else '', value_style)],
            [Paragraph('Fahrzeugwert Brutto:', label_style), Paragraph(f"{self.vehicle_value:,.2f} €" if self.vehicle_value else '', value_style)],
            [Paragraph('Vorsteuerabzugsberechtigung:', label_style), Paragraph(self._get_has_vat_deduction_display(), value_style)],
            [Paragraph('Zahlart des Fahrzeuges:', label_style), Paragraph(self._get_payment_type_display(), value_style)],
        ]

        value_table = Table(value_data, colWidths=[3.2*inch, 3.3*inch])
        value_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(value_table)
        story.append(Spacer(1, 20))

        # Continue with driver information...
        return self._continue_driver_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

    def _continue_driver_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Continue PDF generation with driver information sections"""

        # Driver Information Section
        story.append(Paragraph("Fahrerinformationen", heading_style))

        driver_data = [
            [Paragraph('Fahrer 1 Name:', label_style), Paragraph(self.driver1_name or '', value_style)],
            [Paragraph('Fahrer 1 Geburtsdatum:', label_style), Paragraph(self.driver1_birthdate.strftime('%d.%m.%Y') if self.driver1_birthdate else '', value_style)],
        ]

        # Add additional drivers if they exist
        if self.has_additional_driver == 'yes' and self.driver2_name:
            driver_data.extend([
                [Paragraph('Fahrer 2 Name:', label_style), Paragraph(self.driver2_name, value_style)],
                [Paragraph('Fahrer 2 Geburtsdatum:', label_style), Paragraph(self.driver2_birthdate.strftime('%d.%m.%Y') if self.driver2_birthdate else '', value_style)],
            ])

        if self.has_driver3 == 'yes' and self.driver3_name:
            driver_data.extend([
                [Paragraph('Fahrer 3 Name:', label_style), Paragraph(self.driver3_name, value_style)],
                [Paragraph('Fahrer 3 Geburtsdatum:', label_style), Paragraph(self.driver3_birthdate.strftime('%d.%m.%Y') if self.driver3_birthdate else '', value_style)],
            ])

        if self.has_driver4 == 'yes' and self.driver4_name:
            driver_data.extend([
                [Paragraph('Fahrer 4 Name:', label_style), Paragraph(self.driver4_name, value_style)],
                [Paragraph('Fahrer 4 Geburtsdatum:', label_style), Paragraph(self.driver4_birthdate.strftime('%d.%m.%Y') if self.driver4_birthdate else '', value_style)],
            ])

        driver_table = Table(driver_data, colWidths=[3.2*inch, 3.3*inch])
        driver_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(driver_table)
        story.append(Spacer(1, 20))

        # Usage & Risk Information Section
        story.append(Paragraph("Nutzung und Risikoinformationen", heading_style))

        usage_data = [
            [Paragraph('Verwendungszweck des Fahrzeuges:', label_style), Paragraph(self._get_usage_purpose_display(), value_style)],
            [Paragraph('Jahresfahrleistung:', label_style), Paragraph(f"{self.annual_mileage:,} km" if self.annual_mileage else '', value_style)],
            [Paragraph('Tracker vorhanden:', label_style), Paragraph(self._get_has_tracker_display(), value_style)],
            [Paragraph('Vorversicherung vorhanden:', label_style), Paragraph(self._get_has_previous_insurance_display(), value_style)],
        ]

        # Add previous insurance details if applicable
        if self.has_previous_insurance == 'yes' and self.previous_insurance_details:
            usage_data.append([Paragraph('Details der Vorversicherung:', label_style), Paragraph(self.previous_insurance_details, value_style)])

        usage_data.append([Paragraph('Vorschäden vorhanden:', label_style), Paragraph(self._get_has_previous_damage_display(), value_style)])

        # Add previous damage details if applicable
        if self.has_previous_damage == 'yes' and self.previous_damage_details:
            usage_data.append([Paragraph('Details der Vorschäden:', label_style), Paragraph(self.previous_damage_details, value_style)])

        usage_data.extend([
            [Paragraph('Weitere vergleichbare Fahrzeuge:', label_style), Paragraph(self._get_has_similar_vehicles_display(), value_style)],
        ])

        # Add similar vehicles description if applicable
        if self.has_similar_vehicles == 'yes' and self.similar_vehicles_description:
            usage_data.append([Paragraph('Beschreibung vergleichbarer Fahrzeuge:', label_style), Paragraph(self.similar_vehicles_description, value_style)])

        usage_table = Table(usage_data, colWidths=[3.2*inch, 3.3*inch])
        usage_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(usage_table)
        story.append(Spacer(1, 20))

        # Continue with additional information...
        return self._continue_additional_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

    def _continue_additional_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Continue PDF generation with additional information sections"""

        # Additional Information Section
        story.append(Paragraph("Weitere Informationen", heading_style))

        additional_data = [
            [Paragraph('Vorerfahrung auf vergleichbaren Fahrzeugen:', label_style), Paragraph(self._get_has_previous_experience_display(), value_style)],
            [Paragraph('Kennzeichen des Alltagsfahrzeuges:', label_style), Paragraph(self.daily_vehicle_license or '', value_style)],
            [Paragraph('Fahrzeugstandort bei Versicherungsnehmer:', label_style), Paragraph(self._get_is_vehicle_at_owner_address_display(), value_style)],
        ]

        additional_table = Table(additional_data, colWidths=[3.2*inch, 3.3*inch])
        additional_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(additional_table)
        story.append(Spacer(1, 20))

        # Vehicle Location Section (only if different from owner address)
        if self.is_vehicle_at_owner_address == 'no':
            story.append(Paragraph("Fahrzeugstandort", heading_style))

            location_data = [
                [Paragraph('Straße (Fahrzeugstandort):', label_style), Paragraph(f"{self.vehicle_street or ''} {self.vehicle_street_number or ''}".strip(), value_style)],
                [Paragraph('Adresszusatz (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_street_addition or '', value_style)],
                [Paragraph('PLZ (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_zip or '', value_style)],
                [Paragraph('Ort (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_city or '', value_style)],
                [Paragraph('Land (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_country_id.name if self.vehicle_country_id else '', value_style)],
            ]

            location_table = Table(location_data, colWidths=[3.2*inch, 3.3*inch])
            location_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(location_table)
            story.append(Spacer(1, 20))

        # Risk Information Section
        story.append(Paragraph("Risikoinformationen", heading_style))

        risk_data = [
            [Paragraph('Risikoannahmevoraussetzung:', label_style), Paragraph(self._get_risk_requirement_display(), value_style)],
            [Paragraph('Einbruchmeldeanlage vorhanden:', label_style), Paragraph(self._get_has_burglar_alarm_display(), value_style)],
            [Paragraph('Brandmeldeanlage vorhanden:', label_style), Paragraph(self._get_has_fire_alarm_display(), value_style)],
        ]

        # Add connection details if alarms are present
        if self.has_burglar_alarm == 'yes' and self.has_alarm_police_connection:
            risk_data.append([Paragraph('Aufschaltung Einbruchmeldeanlage:', label_style), Paragraph(self._get_has_alarm_police_connection_display(), value_style)])

        if self.has_fire_alarm == 'yes' and self.has_fire_department_connection:
            risk_data.append([Paragraph('Aufschaltung Brandmeldeanlage:', label_style), Paragraph(self._get_has_fire_department_connection_display(), value_style)])

        risk_table = Table(risk_data, colWidths=[3.2*inch, 3.3*inch])
        risk_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(risk_table)
        story.append(Spacer(1, 20))

        # Continue with final sections...
        return self._finalize_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

    def _finalize_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Finalize PDF generation with documents, notes, and footer"""

        # Uploaded Documents Section (only if any documents were uploaded)
        uploaded_docs = []
        if self.value_proof and self.value_proof_filename:
            uploaded_docs.append(['Anschaffungsbeleg/Leasingvertrag:', self.value_proof_filename])
        if self.vehicle_documents and self.vehicle_documents_filename:
            uploaded_docs.append(['Fahrzeugunterlagen:', self.vehicle_documents_filename])
        if self.photos and self.photos_filename:
            uploaded_docs.append(['Fotos:', self.photos_filename])

        if uploaded_docs:
            story.append(Paragraph("Hochgeladene Dokumente", heading_style))

            docs_data = [[Paragraph(label, label_style), Paragraph(filename, value_style)] for label, filename in uploaded_docs]
            docs_table = Table(docs_data, colWidths=[3.2*inch, 3.3*inch])
            docs_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(docs_table)
            story.append(Spacer(1, 20))

        # Notes Section (only if provided)
        notes_added = False
        if self.notes and self.notes.strip() and self.notes.strip().lower() not in ['no', 'nein', '']:
            if not notes_added:
                story.append(Paragraph("Weitere Mitteilungen", heading_style))
                notes_added = True

            notes_data = [[Paragraph('Bemerkungen:', label_style), Paragraph(self.notes, value_style)]]
            notes_table = Table(notes_data, colWidths=[3.2*inch, 3.3*inch])
            notes_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(notes_table)
            story.append(Spacer(1, 10))

        # Internal Notes Section (only if provided)
        if self.internal_notes and self.internal_notes.strip() and self.internal_notes.strip().lower() not in ['no', 'nein', '']:
            if not notes_added:
                story.append(Paragraph("Weitere Mitteilungen", heading_style))
                notes_added = True

            internal_notes_data = [[Paragraph('Notizen:', label_style), Paragraph(self.internal_notes, value_style)]]
            internal_notes_table = Table(internal_notes_data, colWidths=[3.2*inch, 3.3*inch])
            internal_notes_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(internal_notes_table)
            story.append(Spacer(1, 10))

        if notes_added:
            story.append(Spacer(1, 10))

        # Footer
        story.append(Spacer(1, 30))
        footer_text = f"Erstellt am: {fields.Datetime.now().strftime('%d.%m.%Y %H:%M')} Uhr"
        footer_style = ParagraphStyle(
            'Footer',
            parent=normal_style,
            fontSize=8,
            textColor=colors.grey,
            alignment=TA_CENTER
        )
        story.append(Paragraph(footer_text, footer_style))

        # Build PDF
        doc.build(story)

        # Get the PDF data
        pdf_data = buffer.getvalue()
        buffer.close()

        # Return base64 encoded PDF
        return base64.b64encode(pdf_data)

    def _format_phone_number(self, phone_number):
        """Format phone number to ensure it displays with country code prefix"""
        if not phone_number:
            return ''

        # If the phone number doesn't start with +, add it
        # (since our controller stores them as international numbers)
        if phone_number and not phone_number.startswith('+'):
            return f'+{phone_number}'

        return phone_number

    # Helper methods for displaying selection field values
    def _get_broker_salutation_display(self):
        """Get display value for broker salutation"""
        salutation_dict = dict(self._fields['broker_salutation'].selection)
        return salutation_dict.get(self.broker_salutation, '')

    def _get_has_customer_authorization_display(self):
        """Get display value for customer authorization"""
        auth_dict = dict(self._fields['has_customer_authorization'].selection)
        return auth_dict.get(self.has_customer_authorization, '')

    def _get_salutation_display(self):
        """Get display value for salutation"""
        salutation_dict = dict(self._fields['salutation'].selection)
        return salutation_dict.get(self.salutation, '')

    def _get_owner_salutation_display(self):
        """Get display value for owner salutation"""
        salutation_dict = dict(self._fields['salutation'].selection)
        return salutation_dict.get(self.owner_salutation, '')

    def _get_is_season_registration_display(self):
        """Get display value for season registration"""
        season_dict = dict(self._fields['is_season_registration'].selection)
        return season_dict.get(self.is_season_registration, '')

    def _get_has_vat_deduction_display(self):
        """Get display value for VAT deduction"""
        vat_dict = dict(self._fields['has_vat_deduction'].selection)
        return vat_dict.get(self.has_vat_deduction, '')

    def _get_payment_type_display(self):
        """Get display value for payment type"""
        payment_dict = dict(self._fields['payment_type'].selection)
        return payment_dict.get(self.payment_type, '')

    def _get_usage_purpose_display(self):
        """Get display value for usage purpose"""
        usage_dict = dict(self._fields['usage_purpose'].selection)
        return usage_dict.get(self.usage_purpose, '')

    def _get_has_tracker_display(self):
        """Get display value for tracker"""
        tracker_dict = dict(self._fields['has_tracker'].selection)
        return tracker_dict.get(self.has_tracker, '')

    def _get_has_previous_insurance_display(self):
        """Get display value for previous insurance"""
        insurance_dict = dict(self._fields['has_previous_insurance'].selection)
        return insurance_dict.get(self.has_previous_insurance, '')

    def _get_has_previous_damage_display(self):
        """Get display value for previous damage"""
        damage_dict = dict(self._fields['has_previous_damage'].selection)
        return damage_dict.get(self.has_previous_damage, '')

    def _get_has_similar_vehicles_display(self):
        """Get display value for similar vehicles"""
        similar_dict = dict(self._fields['has_similar_vehicles'].selection)
        return similar_dict.get(self.has_similar_vehicles, '')

    def _get_has_previous_experience_display(self):
        """Get display value for previous experience"""
        experience_dict = dict(self._fields['has_previous_experience'].selection)
        return experience_dict.get(self.has_previous_experience, '')

    def _get_is_vehicle_at_owner_address_display(self):
        """Get display value for vehicle at owner address"""
        location_dict = dict(self._fields['is_vehicle_at_owner_address'].selection)
        return location_dict.get(self.is_vehicle_at_owner_address, '')

    def _get_risk_requirement_display(self):
        """Get display value for risk requirement"""
        risk_dict = dict(self._fields['risk_requirement'].selection)
        return risk_dict.get(self.risk_requirement, '')

    def _get_has_burglar_alarm_display(self):
        """Get display value for burglar alarm"""
        alarm_dict = dict(self._fields['has_burglar_alarm'].selection)
        return alarm_dict.get(self.has_burglar_alarm, '')

    def _get_has_fire_alarm_display(self):
        """Get display value for fire alarm"""
        fire_dict = dict(self._fields['has_fire_alarm'].selection)
        return fire_dict.get(self.has_fire_alarm, '')

    def _get_has_alarm_police_connection_display(self):
        """Get display value for alarm police connection"""
        connection_dict = dict(self._fields['has_alarm_police_connection'].selection)
        return connection_dict.get(self.has_alarm_police_connection, '')

    def _get_has_fire_department_connection_display(self):
        """Get display value for fire department connection"""
        fire_connection_dict = dict(self._fields['has_fire_department_connection'].selection)
        return fire_connection_dict.get(self.has_fire_department_connection, '')