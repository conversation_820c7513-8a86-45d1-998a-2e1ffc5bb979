from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from datetime import date
import logging
import traceback
import io
import base64

# Try to import reportlab, but make it optional
try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.platypus import SimpleDocTemplate, BaseDocTemplate, PageTemplate, Frame, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib import colors
    from reportlab.lib.units import inch
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

_logger = logging.getLogger(__name__)

# Define mapping for Selection fields between Premium Car form and CRM lead
SELECTION_MAPS = {
    'usage_purpose': {
        'mostly_private': 'only_private',
        'mostly_business': 'only_professional',
        'exclusively_private': 'only_private',
        'exclusively_business': 'only_professional',
        'private': 'only_private',  # Keep for backward compatibility
        'business': 'only_professional',  # Keep for backward compatibility
        'mixed': 'mainly_private',  # Keep for backward compatibility
    },
    'risk_acceptance_condition': {
        'single_garage': 'singe_or_double_garage',
        'communal_garage': 'collective_garage_or_hall',
        'no_garage': 'no_lockable_parking_space',
    },
    'payment_type_for_vehicle': {
        'cash': 'cash_payment',
        'financing': 'financing',
        'leasing': 'leasing',
    },
    'policy_holder_gender': {
        'male': 'male',
        'female': 'female',
        'other': 'others',  # Map 'other' to 'others'
        'diverse': 'others',  # Map 'diverse' to 'others'
        'company': 'others',  # Map 'company' to 'others'
    },
    'burglary_alarm_system': {
        True: 'burglary_alarm_system',
        False: 'no_burglary_alarm_system',
        'yes': 'burglary_alarm_system',
        'no': 'no_burglary_alarm_system',
    },
    'fire_alarm_system': {
        True: 'fire_alarm_system',
        False: 'no_fire_alarm_system',
        'yes': 'fire_alarm_system',
        'no': 'no_fire_alarm_system',
    },
    # Add more mappings as needed
}

class PremiumCarEndkunden(models.Model):
    _name = 'premiumcar.endkunden'
    _description = 'Premium Car Erstanfrage Endkunden'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string="Request Reference", required=True, copy=False, readonly=True,
                       default=lambda self: 'New')

    # Insurance Start Date
    insurance_start_date = fields.Date('Versicherungsbeginn', required=True)

    # Policyholder Information
    salutation = fields.Selection([
        ('mr', 'Herr'),
        ('mrs', 'Frau'),
        ('diverse', 'Divers'),
        ('company', 'Firma'),
        ('other', 'Sonstige')
    ], string='Anrede', required=True)
    first_name = fields.Char('Vorname', required=True)
    last_name = fields.Char('Nachname', required=True)
    street = fields.Char('Straße', required=True)
    street_number = fields.Char('Nr.', required=True)
    street_addition = fields.Char('Adresszusatz', required=True)
    zip = fields.Char('PLZ', required=True)
    city = fields.Char('Ort', required=True)
    country_id = fields.Many2one('res.country', string='Land', required=True,
                                 default=lambda self: self.env['res.country'].search([('code', '=', 'DE')], limit=1).id)
    email = fields.Char('E-Mail')
    phone = fields.Char('Telefonnummer')
    mobile = fields.Char('Mobilfunknummer')

    # Owner different from policyholder
    is_owner_different = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Halter abweichend vom Versicherungsnehmer?', required=True)

    # Owner Information
    owner_salutation = fields.Selection([
        ('mr', 'Herr'),
        ('mrs', 'Frau'),
        ('diverse', 'Divers'),
        ('company', 'Firma'),
        ('other', 'Sonstige')
    ], string='Anrede (Halter)')
    owner_first_name = fields.Char('Vorname (Halter)')
    owner_last_name = fields.Char('Nachname (Halter)')
    owner_street = fields.Char('Straße (Halter)')
    owner_street_number = fields.Char('Nr. (Halter)')
    owner_street_addition = fields.Char('Adresszusatz (Halter)')
    owner_zip = fields.Char('PLZ (Halter)')
    owner_city = fields.Char('Ort (Halter)')
    owner_country_id = fields.Many2one('res.country', string='Land (Halter)',
                                     default=lambda self: self.env['res.country'].search([('code', '=', 'DE')], limit=1).id)
    owner_email = fields.Char('E-Mail (Halter)')
    owner_phone = fields.Char('Telefonnummer (Halter)')
    owner_mobile = fields.Char('Mobilfunknummer (Halter)')

    # Vehicle Information
    car_manufacturer_id = fields.Many2one('vehicle.manufacturer', string='Fahrzeughersteller', required=True)
    car_type = fields.Char('Fahrzeugtyp', required=True)
    license_plate = fields.Char('Amtliches Kennzeichen')

    # Season Registration
    is_season_registration = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Wird das Fahrzeug auf Saison zugelassen?', required=True)
    seasonal_period_from = fields.Date(string='Saisonzeitraum von', tracking=True)
    seasonal_period_to = fields.Date(string='Saisonzeitraum bis', tracking=True)

    # Registration dates
    first_registration_date = fields.Date('Erstzulassungsdatum', required=True)
    owner_registration_date = fields.Date('Zulassungsdatum auf den Versicherungsnehmer', required=True)

    # Vehicle condition and value
    current_mileage = fields.Integer('Aktueller km-Stand', required=True)
    vehicle_value = fields.Float('Fahrzeugwert Brutto', required=True)

    # VAT deduction
    has_vat_deduction = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besteht Vorsteuerabzugsberechtigung?', required=True)

    # Payment type
    payment_type = fields.Selection([
        ('cash', 'Barzahlung'),
        ('financing', 'Finanzierung'),
        ('leasing', 'Leasing')
    ], string='Zahlart des Fahrzeuges', required=True)

    # Driver information
    driver1_name = fields.Char('Vor- und Nachname Fahrer 1', required=False)
    driver1_birthdate = fields.Date('Geburtsdatum Fahrer 1', required=False)

    # Additional driver
    has_additional_driver = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Möchten Sie einen weiteren Fahrer benennen?', required=True)

    # Additional Driver Information
    driver2_name = fields.Char(string='Name Fahrer 2', tracking=True)
    driver2_birthdate = fields.Date(string='Geburtsdatum Fahrer 2', tracking=True)
    has_driver3 = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Weiterer Fahrer 3', tracking=True)
    driver3_name = fields.Char(string='Name Fahrer 3', tracking=True)
    driver3_birthdate = fields.Date(string='Geburtsdatum Fahrer 3', tracking=True)
    has_driver4 = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Weiterer Fahrer 4', tracking=True)
    driver4_name = fields.Char(string='Name Fahrer 4', tracking=True)
    driver4_birthdate = fields.Date(string='Geburtsdatum Fahrer 4', tracking=True)

    # Vehicle usage
    usage_purpose = fields.Selection([
        ('mostly_private', 'überwiegend privat'),
        ('mostly_business', 'überwiegend beruflich'),
        ('exclusively_private', 'ausschließlich privat'),
        ('exclusively_business', 'ausschließlich beruflich')
    ], string='Verwendungszweck des Fahrzeuges', required=True)

    # Annual mileage
    annual_mileage = fields.Integer('Jahresfahrleistung', required=True)

    # Security features
    has_tracker = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Tracker vorhanden?', required=True)

    # Previous insurance
    has_previous_insurance = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Vorversicherung vorhanden?', required=True)
    previous_insurance_details = fields.Text(string='Versicherer und Versicherungsscheinnummer der Vorversicherung', tracking=True)

    # Previous damage
    has_previous_damage = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Sind Vorschäden im Bereich KFZ vorhanden?', required=True)
    liability_damage_count = fields.Integer(string='Anzahl Vorschäden Haftpflicht', tracking=True)
    full_coverage_damage_count = fields.Integer(string='Anzahl Vorschäden Vollkasko', tracking=True)
    partial_coverage_damage_count = fields.Integer(string='Anzahl Vorschäden Teilkasko', tracking=True)

    # Similar vehicles
    has_similar_vehicles = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besitzen Sie weitere vergleichbare Fahrzeuge?', required=True)
    similar_vehicles_description = fields.Text(string='Beschreibung der vergleichbaren Fahrzeuge', tracking=True)

    # Previous driving experience
    has_previous_experience = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Haben Sie Vorerfahrung (Fahrerfahrung) auf vergleichbaren Fahrzeugen?', required=True)

    # Daily vehicle license plate
    daily_vehicle_license = fields.Char('Kennzeichen des Alltagsfahrzeuges')

    # Vehicle location
    is_vehicle_at_owner_address = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Überwiegender Fahrzeugstandort unter der Anschrift des Versicherungsnehmers?', required=True)

    # Vehicle location fields when different from owner address
    vehicle_street = fields.Char(string='Straße (Fahrzeugstandort)', tracking=True)
    vehicle_street_number = fields.Char(string='Nr. (Fahrzeugstandort)', tracking=True)
    vehicle_street_addition = fields.Char(string='Adresszusatz (Fahrzeugstandort)', tracking=True)
    vehicle_zip = fields.Char(string='PLZ (Fahrzeugstandort)', tracking=True)
    vehicle_city = fields.Char(string='Ort (Fahrzeugstandort)', tracking=True)
    vehicle_country_id = fields.Many2one('res.country', string='Land (Fahrzeugstandort)', tracking=True)

    # Risk requirements
    risk_requirement = fields.Selection([
        ('single_garage', 'Einzel-/Doppelgarage'),
        ('communal_garage', 'Sammelgarage/Halle'),
        ('no_garage', 'keine abschließbare, überdachte und durch feste Wände umschlossene Abstellmöglichkeit')
    ], string='Risikoannahmevoraussetzung für das Fahrzeug', required=True)

    # Security systems
    has_burglar_alarm = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Ist eine Einbruchmeldeanlage vorhanden?', required=True)

    has_alarm_police_connection = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besteht eine Aufschaltung (z.B. zur Polizei)', tracking=True)

    has_fire_alarm = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Ist eine Brandmeldeanlage (VdS anerkannt) vorhanden?', required=True)

    has_fire_department_connection = fields.Selection([
        ('yes', 'Ja'),
        ('no', 'Nein')
    ], string='Besteht eine Aufschaltung zur Feuerwehr', tracking=True)

    # Documents
    value_proof = fields.Binary('Wertnachweis', attachment=True)
    value_proof_filename = fields.Char('Wertnachweis Dateiname')

    vehicle_documents = fields.Binary('Fahrzeugunterlagen', attachment=True)
    vehicle_documents_filename = fields.Char('Fahrzeugunterlagen Dateiname')

    photos = fields.Binary('Fotos', attachment=True)
    photos_filename = fields.Char('Fotos Dateiname')

    # Additional notes
    notes = fields.Text('Weitere Mitteilungen')

    # Status
    state = fields.Selection([
        ('draft', 'Entwurf'),
        ('submitted', 'Eingereicht'),
        ('processed', 'Bearbeitet')
    ], default='draft', string='Status', tracking=True)

    # Link to CRM Lead
    lead_id = fields.Many2one('crm.lead', string='Related Lead', ondelete='set null')

    # Link to Contact
    partner_id = fields.Many2one('res.partner', string='Related Contact', ondelete='set null')

    # Company field for multi-company access control
    company_id = fields.Many2one('res.company', string='Company',
                                default=lambda self: self.env.company,
                                help="Company that this request belongs to")

    @api.model_create_multi
    def create(self, vals_list):
        # Import logging at the top level to avoid repeated imports
        import logging
        import traceback
        _logger = logging.getLogger(__name__)

        # Log the start of the create process
        _logger.info("========== PREMIUM CAR ENDKUNDEN CREATE START ==========")

        # Create a new list to store the modified values
        new_vals_list = []

        for vals in vals_list:
            _logger.info("Creating premium car request record with values: %s", {k: v for k, v in vals.items() if k not in ['value_proof', 'vehicle_documents']})

            # Generate sequence number if needed
            if vals.get('name', 'New') == 'New':
                vals['name'] = self.env['ir.sequence'].next_by_code('premiumcar.endkunden') or 'New'
                _logger.debug(f"Generated new sequence: {vals['name']}")

            # Create a contact record for this customer
            # Generate a unique reference code
            timestamp = fields.Datetime.now().strftime('%Y%m%d%H%M%S')
            reference_code = f"PC-{timestamp}"

            # Use clean name without timestamp
            clean_name = f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip()
            if not clean_name:
                _logger.warning("Missing first_name or last_name for contact creation")
                clean_name = f"Unknown Contact ({timestamp})"

            partner_vals = {
                'name': clean_name,
                'ref': reference_code, # Store the reference code in the standard reference field
                'street': vals.get('street', ''),
                'street2': vals.get('street_addition', ''),
                'zip': vals.get('zip', ''),
                'city': vals.get('city', ''),
                'country_id': vals.get('country_id'),
                'email': vals.get('email', ''),
                'phone': vals.get('phone', ''),
                'mobile': vals.get('mobile', ''),
                'type': 'contact',
                'company_type': 'person',
                # 'customer_rank': 1,  # Removed - not available in this Odoo version
                'comment': f"Created from Premium Car form {vals.get('name', 'New')}",
                'function': 'Fahrzeugversicherung',  # Add job position
            }

            # Set the proper title based on salutation
            if vals.get('salutation') == 'mr':
                title = self.env.ref('base.res_partner_title_mister', False)
                if title:
                    partner_vals['title'] = title.id
            elif vals.get('salutation') == 'mrs':
                title = self.env.ref('base.res_partner_title_madam', False)
                if title:
                    partner_vals['title'] = title.id

            # Log the partner values for debugging
            _logger.info("Attempting to create contact with values: %s", partner_vals)

            # Log partner values for debugging
            _logger.debug("PARTNER_VALS=%r", partner_vals)

            # Make sure all required fields are present
            if not partner_vals.get('name'):
                _logger.error("Cannot create contact: Missing name")
                # Generate a fallback name if missing
                partner_vals['name'] = f"Unknown Contact ({fields.Datetime.now()})"

            # Check if the partner already exists to avoid duplicates
            existing_partner = False
            if partner_vals.get('email'):
                existing_partner = self.env['res.partner'].sudo().search([
                    ('email', '=', partner_vals.get('email'))
                ], limit=1)
                if existing_partner:
                    _logger.info(f"Found existing partner with email {partner_vals.get('email')}: ID {existing_partner.id}")

            if not existing_partner and partner_vals.get('phone'):
                existing_partner = self.env['res.partner'].sudo().search([
                    ('phone', '=', partner_vals.get('phone'))
                ], limit=1)
                if existing_partner:
                    _logger.info(f"Found existing partner with phone {partner_vals.get('phone')}: ID {existing_partner.id}")

            if existing_partner:
                # Use existing partner instead of creating a new one
                vals['partner_id'] = existing_partner.id
                _logger.info(f"Using existing partner (ID: {existing_partner.id}) instead of creating a new one")
            else:
                # Define mail context to disable auto-followers
                MailCtx = {
                    'mail_create_nolog': True,
                    'mail_notrack': True,
                    'tracking_disable': True,
                    'mail_auto_subscription': False,
                    'disable_duplicate_check': True,
                }

                # Create the partner with a savepoint to ensure transaction integrity
                with self.env.cr.savepoint():
                    partner = self.env['res.partner'].sudo().with_context(**MailCtx).create(partner_vals)
                    vals['partner_id'] = partner.id
                    _logger.info("PARTNER CREATED id=%s", partner.id)

            # Create form data for mapper with consistent field names
            _logger.info("Preparing CRM lead data from premium car form")
            form_data = {
                # Contact information
                'first_name': vals.get('first_name'),
                'last_name': vals.get('last_name'),
                'email': vals.get('email'),
                'phone': vals.get('phone'),
                'mobile': vals.get('mobile'),

                # Add salutation for gender mapping
                'salutation': vals.get('salutation'),

                # Add reference code for description
                'reference_code': reference_code,

                # Owner information
                'owner_salutation': vals.get('owner_salutation'),
                'owner_first_name': vals.get('owner_first_name'),
                'owner_last_name': vals.get('owner_last_name'),
                'owner_street': vals.get('owner_street'),
                'owner_street_number': vals.get('owner_street_number'),
                'owner_street_addition': vals.get('owner_street_addition'),
                'owner_zip': vals.get('owner_zip'),
                'owner_city': vals.get('owner_city'),
                'owner_country_id': vals.get('owner_country_id'),
                'owner_email': vals.get('owner_email'),
                'owner_phone': vals.get('owner_phone'),
                'owner_mobile': vals.get('owner_mobile'),

                # Add birth date mapping - use driver1_birthdate as the main birthdate
                # Only set date fields if driver1_birthdate is a valid date
                'birth_date': vals.get('driver1_birthdate') if isinstance(vals.get('driver1_birthdate'), date) else False,
                'policy_holder_dob': vals.get('driver1_birthdate') if isinstance(vals.get('driver1_birthdate'), date) else False,  # Map to CRM field directly

                # Address information
                'street': vals.get('street'),
                'street_number': vals.get('street_number'),
                'street_addition': vals.get('street_addition'),
                'zip': vals.get('zip'),
                'city': vals.get('city'),
                'country_id': vals.get('country_id'),

                # Vehicle information
                'car_manufacturer_id': vals.get('car_manufacturer_id'),
                'car_type': vals.get('car_type'),
                'license_plate': vals.get('license_plate'),
                'first_registration_date': vals.get('first_registration_date'),
                'owner_registration_date': vals.get('owner_registration_date'),
                'vehicle_value': vals.get('vehicle_value'),
                'current_mileage': vals.get('current_mileage'),
                'annual_mileage': vals.get('annual_mileage'),

                # Insurance and ownership information
                'insurance_start_date': vals.get('insurance_start_date'),
                'is_owner_different': vals.get('is_owner_different'),
                'divergent_vehicle_owner': vals.get('is_owner_different') == 'yes',  # Map to CRM field
                'is_season_registration': vals.get('is_season_registration'),
                'seasonal_registration': vals.get('is_season_registration') == 'yes',  # Map to CRM field
                # Map seasonal period fields if they exist in CRM
                'seasonal_period_from': vals.get('seasonal_period_from'),
                'seasonal_period_to': vals.get('seasonal_period_to'),
                'payment_type': vals.get('payment_type'),
                'payment_type_for_vehicle': vals.get('payment_type'),  # Map to CRM field
                'has_vat_deduction': vals.get('has_vat_deduction'),
                'input_tax_deduction_entitlement': vals.get('has_vat_deduction') == 'yes',  # Map to CRM field
                'usage_purpose': vals.get('usage_purpose'),

                # Driver information
                'driver1_name': vals.get('driver1_name'),
                'driver1_birthdate': vals.get('driver1_birthdate'),
                'driver_1': vals.get('driver1_name'),  # Map to CRM field
                'dob_driver_1': vals.get('driver1_birthdate'),  # Map to CRM field
                # Set driver_2 if has_additional_driver is 'yes'
                'driver_2': vals.get('driver2_name') if vals.get('has_additional_driver') == 'yes' else None,
                'dob_driver_2': vals.get('driver2_birthdate') if vals.get('has_additional_driver') == 'yes' else None,
                # Set driver_3 if has_driver3 is 'yes'
                'driver_3': vals.get('driver3_name') if vals.get('has_driver3') == 'yes' else None,
                'dob_driver_3': vals.get('driver3_birthdate') if vals.get('has_driver3') == 'yes' else None,
                # Set driver_4 if has_driver4 is 'yes'
                'driver_4': vals.get('driver4_name') if vals.get('has_driver4') == 'yes' else None,
                'dob_driver_4': vals.get('driver4_birthdate') if vals.get('has_driver4') == 'yes' else None,

                # Risk and security information
                'has_tracker': vals.get('has_tracker'),
                'tracker': vals.get('has_tracker') == 'yes',  # Map to CRM field
                'has_previous_insurance': vals.get('has_previous_insurance'),
                'existing_insurance': vals.get('has_previous_insurance') == 'yes',  # Map to CRM field
                # Map previous_insurance_details to previous_insurer_and_policy_number if it exists in CRM
                'previous_insurer_and_policy_number': vals.get('previous_insurance_details'),
                'has_previous_damage': vals.get('has_previous_damage'),
                'previous_claims': vals.get('has_previous_damage') == 'yes',  # Map to CRM field
                # Map damage count fields if they exist in CRM
                'number_of_claim_in_motor_liability_insurance': vals.get('liability_damage_count', 0),
                'number_of_claims_in_comprehensive_insurance': vals.get('full_coverage_damage_count', 0),
                'number_of_claims_in_partial_coverage_insurance': vals.get('partial_coverage_damage_count', 0),
                'has_similar_vehicles': vals.get('has_similar_vehicles'),
                'owning_comparable_vehicles': vals.get('has_similar_vehicles') == 'yes',  # Map to CRM field
                # Map similar_vehicles_description to comparable_vehicles if it exists in CRM
                'comparable_vehicles': vals.get('similar_vehicles_description'),
                # Add reference code to form_data for the opportunity mapper
                'website_ref_number': reference_code,
                'has_previous_experience': vals.get('has_previous_experience'),
                'driving_experience_with_comparable_vehicle': vals.get('has_previous_experience') == 'yes',  # Map to CRM field
                'daily_vehicle_license': vals.get('daily_vehicle_license'),
                'license_plate_of_daily_use_vehicle': vals.get('daily_vehicle_license'),  # Map to CRM field
                'is_vehicle_at_owner_address': vals.get('is_vehicle_at_owner_address'),
                'main_vehicle_location_at_policyholder_address': vals.get('is_vehicle_at_owner_address') == 'yes',  # Map to CRM field
                # Store vehicle location fields only in our custom model, not in CRM
                # These fields don't exist in the CRM model
                'risk_requirement': vals.get('risk_requirement'),
                'risk_acceptance_condition': vals.get('risk_requirement'),  # Map to CRM field
                'has_burglar_alarm': vals.get('has_burglar_alarm'),
                'burglary_alarm_system': SELECTION_MAPS['burglary_alarm_system'].get(vals.get('has_burglar_alarm') == 'yes', 'no_burglary_alarm_system'),  # Map to CRM field
                'has_alarm_police_connection': vals.get('has_alarm_police_connection'),
                'has_fire_alarm': vals.get('has_fire_alarm'),
                'fire_alarm_system': SELECTION_MAPS['fire_alarm_system'].get(vals.get('has_fire_alarm') == 'yes', 'no_fire_alarm_system'),  # Map to CRM field
                'has_fire_department_connection': vals.get('has_fire_department_connection'),

                # Vehicle details
                'vehicle_manufacturer': vals.get('car_manufacturer_id'),  # Map to CRM field
                'vehicle_type': vals.get('car_type'),  # Map to CRM field
                'vehicle_value': vals.get('vehicle_value'),  # Map to CRM field
                'current_milage': vals.get('current_mileage'),  # Map to CRM field (note the spelling difference)
                'annual_milage': vals.get('annual_mileage'),  # Map to CRM field (note the spelling difference)
                'first_registration': vals.get('first_registration_date'),  # Map to CRM field
                'registration_on_policyholder': vals.get('owner_registration_date'),  # Map to CRM field

                # Set policy category to 'vehicle' for Premium Car
                'policy_category': 'vehicle',
                'category': 'vehicle',  # Map to CRM field

                # Map salutation to policy_holder_gender using SELECTION_MAPS
                'policy_holder_gender': SELECTION_MAPS['policy_holder_gender'].get(
                    'male' if vals.get('salutation') == 'mr' else
                    'female' if vals.get('salutation') == 'mrs' else
                    'diverse' if vals.get('salutation') == 'diverse' else
                    'company' if vals.get('salutation') == 'company' else
                    'other'
                ),

                # Additional information
                'notes': vals.get('notes'),
                'other_information': vals.get('notes'),  # Map to CRM field
            }

            # Check if opportunity mapper exists
            mapper_exists = False
            try:
                # Check for model existence more thoroughly
                mapper_exists = self.env['opportunity.mapper'] and True
                _logger.info("opportunity.mapper model is available")
            except Exception as mapper_e:
                _logger.error("Error checking for opportunity.mapper model: %s", str(mapper_e))

            if not mapper_exists:
                _logger.info("opportunity.mapper model is not available, using fallback lead creation")
                # Map salutation to gender using SELECTION_MAPS
                salutation_gender_map = {
                    'mr': 'male',
                    'mrs': 'female',
                    'diverse': 'diverse',
                    'company': 'company',
                    'other': 'other'
                }
                raw_gender = salutation_gender_map.get(vals.get('salutation'), 'other')
                gender = SELECTION_MAPS['policy_holder_gender'].get(raw_gender, 'others')

                # Create a basic lead_vals without the mapper
                lead_vals = {
                    'name': f"Premium Car Endkunden - {vals.get('first_name', '')} {vals.get('last_name', '')}",
                    'type': 'lead',
                    'contact_name': f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip(),
                    'email_from': vals.get('email', ''),
                    'phone': vals.get('phone', ''),
                    'website_ref_number': reference_code,  # Add reference code to website_ref_number field
                    'description': f"Created from PremiumCar erstanfrage endkunden form {reference_code}\n\n" +
                                  f"Premium Car request from {vals.get('first_name', '')} {vals.get('last_name', '')}\n\n" +
                                  f"Form details:\n- Vehicle: {vals.get('car_type', '')}\n- Value: {vals.get('vehicle_value', '')}\n" +
                                  f"- Insurance start: {vals.get('insurance_start_date', '')}"
                }

                # Add address information
                if vals.get('street'):
                    lead_vals['street'] = vals.get('street')
                if vals.get('street_addition'):
                    lead_vals['street2'] = vals.get('street_addition')
                if vals.get('zip'):
                    lead_vals['zip'] = vals.get('zip')
                if vals.get('city'):
                    lead_vals['city'] = vals.get('city')
                if vals.get('country_id'):
                    lead_vals['country_id'] = vals.get('country_id')

                # Add vehicle information
                lead_vals['vehicle_type'] = vals.get('car_type')
                lead_vals['vehicle_value'] = vals.get('vehicle_value')
                lead_vals['vehicle_manufacturer'] = vals.get('car_manufacturer_id')
                lead_vals['license_plate'] = vals.get('license_plate')
                lead_vals['first_registration'] = vals.get('first_registration_date')
                lead_vals['registration_on_policyholder'] = vals.get('owner_registration_date')
                lead_vals['current_milage'] = vals.get('current_mileage')
                lead_vals['annual_milage'] = vals.get('annual_mileage')

                # Add insurance information
                lead_vals['insurance_start_date'] = vals.get('insurance_start_date')
                lead_vals['divergent_vehicle_owner'] = vals.get('is_owner_different') == 'yes'
                lead_vals['seasonal_registration'] = vals.get('is_season_registration') == 'yes'
                # Include seasonal period fields if they exist in the CRM model
                crm_fields = self.env['crm.lead'].sudo().fields_get()
                if 'seasonal_period_from' in crm_fields:
                    lead_vals['seasonal_period_from'] = vals.get('seasonal_period_from')
                if 'seasonal_period_to' in crm_fields:
                    lead_vals['seasonal_period_to'] = vals.get('seasonal_period_to')
                lead_vals['payment_type_for_vehicle'] = vals.get('payment_type')
                lead_vals['input_tax_deduction_entitlement'] = vals.get('has_vat_deduction') == 'yes'
                lead_vals['usage_purpose'] = vals.get('usage_purpose')

                # Add driver information
                lead_vals['driver_1'] = vals.get('driver1_name')
                lead_vals['dob_driver_1'] = vals.get('driver1_birthdate')

                # Add additional driver information if available
                if vals.get('has_additional_driver') == 'yes' and vals.get('driver2_name'):
                    lead_vals['driver_2'] = vals.get('driver2_name')
                    lead_vals['dob_driver_2'] = vals.get('driver2_birthdate')

                # Add driver 3 information if available
                if vals.get('has_driver3') == 'yes' and vals.get('driver3_name'):
                    lead_vals['driver_3'] = vals.get('driver3_name')
                    lead_vals['dob_driver_3'] = vals.get('driver3_birthdate')

                # Add driver 4 information if available
                if vals.get('has_driver4') == 'yes' and vals.get('driver4_name'):
                    lead_vals['driver_4'] = vals.get('driver4_name')
                    lead_vals['dob_driver_4'] = vals.get('driver4_birthdate')

                # Add risk information
                lead_vals['tracker'] = vals.get('has_tracker') == 'yes'
                lead_vals['existing_insurance'] = vals.get('has_previous_insurance') == 'yes'
                lead_vals['previous_claims'] = vals.get('has_previous_damage') == 'yes'
                lead_vals['owning_comparable_vehicles'] = vals.get('has_similar_vehicles') == 'yes'
                lead_vals['driving_experience_with_comparable_vehicle'] = vals.get('has_previous_experience') == 'yes'
                lead_vals['license_plate_of_daily_use_vehicle'] = vals.get('daily_vehicle_license')
                lead_vals['main_vehicle_location_at_policyholder_address'] = vals.get('is_vehicle_at_owner_address') == 'yes'

                # Vehicle location fields are stored only in our custom model, not in CRM
                # as these fields don't exist in the CRM model

                # Map risk_requirement to risk_acceptance_condition
                lead_vals['risk_acceptance_condition'] = SELECTION_MAPS['risk_acceptance_condition'].get(
                    vals.get('risk_requirement'),
                    vals.get('risk_requirement')
                )

                # Map alarm systems using SELECTION_MAPS
                lead_vals['burglary_alarm_system'] = SELECTION_MAPS['burglary_alarm_system'].get(
                    vals.get('has_burglar_alarm') == 'yes', 'no_burglary_alarm_system')
                lead_vals['fire_alarm_system'] = SELECTION_MAPS['fire_alarm_system'].get(
                    vals.get('has_fire_alarm') == 'yes', 'no_fire_alarm_system')

                # Add other information
                lead_vals['other_information'] = vals.get('notes')

                # Check if the gender field exists in the model before setting it
                crm_fields = self.env['crm.lead'].sudo().fields_get()
                if 'gender' in crm_fields:
                    lead_vals['gender'] = gender
                elif 'policy_holder_gender' in crm_fields:
                    lead_vals['policy_holder_gender'] = gender

                # Only set date fields if driver1_birthdate is a valid date
                if isinstance(vals.get('driver1_birthdate'), date):
                    # Set policy_holder_dob field (mandatory field from screenshot)
                    lead_vals['policy_holder_dob'] = vals.get('driver1_birthdate')

                    # Also set other birth date fields if they exist in the model
                    if 'birth_date' in crm_fields:
                        lead_vals['birth_date'] = vals.get('driver1_birthdate')
                    if 'dob_policy_holder' in crm_fields:
                        lead_vals['dob_policy_holder'] = vals.get('driver1_birthdate')
                else:
                    # If no valid date, set to False to avoid type errors
                    lead_vals['policy_holder_dob'] = False
                    if 'birth_date' in crm_fields:
                        lead_vals['birth_date'] = False
                    if 'dob_policy_holder' in crm_fields:
                        lead_vals['dob_policy_holder'] = False

                # Set policy category to 'vehicle' for Premium Car
                if 'policy_category' in crm_fields:
                    lead_vals['policy_category'] = 'vehicle'
                elif 'insurance_category_id' in crm_fields:
                    # Try to find the vehicle insurance category
                    vehicle_category = self.env['insurance.category'].sudo().search(
                        [('category', '=', 'vehicle')], limit=1)
                    if vehicle_category:
                        lead_vals['insurance_category_id'] = vehicle_category.id
            else:
                # Use the opportunity mapper
                _logger.info("Using opportunity.mapper to map form data to CRM lead")
                lead_vals = self.env['opportunity.mapper'].map_form_to_opportunity(form_data, 'premium_car')
                _logger.info("Successfully mapped form data to CRM lead with %s fields", len(lead_vals))

                # Get CRM fields to check if all fields exist
                crm_fields = self.env['crm.lead'].sudo().fields_get()

                # Remove any fields that don't exist in the CRM model
                fields_to_remove = []
                for field in lead_vals.keys():
                    if field not in crm_fields:
                        fields_to_remove.append(field)

                for field in fields_to_remove:
                    _logger.warning(f"Removing field '{field}' from lead_vals as it doesn't exist in CRM model")
                    lead_vals.pop(field, None)

            # Ensure basic lead information is set
            if not lead_vals.get('name'):
                lead_vals['name'] = f"Premium Car Endkunden - {vals.get('first_name', '')} {vals.get('last_name', '')}"
            if not lead_vals.get('type'):
                lead_vals['type'] = 'lead'  # Create as lead, not opportunity

            # Ensure website_ref_number is set
            if not lead_vals.get('website_ref_number'):
                lead_vals['website_ref_number'] = reference_code

            # Update description to include reference code if using opportunity mapper
            if mapper_exists:
                # Prepend reference code to description if it exists
                if lead_vals.get('description'):
                    lead_vals['description'] = f"Created from PremiumCar erstanfrage endkunden form {reference_code}\n\n" + lead_vals['description']
                else:
                    lead_vals['description'] = f"Created from PremiumCar erstanfrage endkunden form {reference_code}"

            # Link to partner if created
            if vals.get('partner_id'):
                lead_vals['partner_id'] = vals.get('partner_id')
                _logger.debug(f"Linked lead to partner ID {vals.get('partner_id')}")

            # Add address information if not already set by mapper
            if not lead_vals.get('street'):
                lead_vals['street'] = (vals.get('street', '') or '') + (' ' + (vals.get('street_number', '') or '') if vals.get('street_number') else '')
            if not lead_vals.get('street2'):
                lead_vals['street2'] = vals.get('street_addition', '')
            if not lead_vals.get('zip'):
                lead_vals['zip'] = vals.get('zip', '')
            if not lead_vals.get('city'):
                lead_vals['city'] = vals.get('city', '')
            if not lead_vals.get('country_id') and vals.get('country_id'):
                lead_vals['country_id'] = vals.get('country_id')

            # Get CRM fields to check if fields exist
            crm_fields = self.env['crm.lead'].sudo().fields_get()

            # Apply all field mappings from SELECTION_MAPS
            for field, mapping in SELECTION_MAPS.items():
                if field in lead_vals:
                    # Check if the field exists in the CRM model before mapping
                    if field in crm_fields:
                        lead_vals[field] = mapping.get(
                            lead_vals[field],
                            lead_vals[field],
                        )
                        if lead_vals[field] not in mapping.values():
                            _logger.warning(f"Unsupported value {lead_vals[field]} for {field}, using as is")
                    else:
                        # Remove fields that don't exist in the CRM model
                        _logger.info(f"Removing field {field} from lead_vals as it doesn't exist in CRM model")
                        lead_vals.pop(field, None)

            # Always remove message_follower_ids to avoid duplicates
            # Rely on Odoo's auto-subscription via partner_id / email_from
            lead_vals.pop('message_follower_ids', None)

            # Also remove message_follower_ids from the original vals to prevent any later addition
            if 'message_follower_ids' in vals:
                vals.pop('message_follower_ids', None)

            _logger.debug("Removed message_follower_ids to avoid duplicate followers")

            # Log the follower commands for debugging
            _logger.debug("FOLLOWERS AFTER CLEAN = %r", lead_vals.get('message_follower_ids'))

            # Log the lead values for debugging
            _logger.debug("LEAD_VALS=%r", lead_vals)

            # Define mail context to disable auto-followers
            MailCtx = {
                'mail_create_nolog': True,
                'mail_notrack': True,
                'tracking_disable': True,
                'mail_auto_subscription': False,
            }

            # Create the lead/opportunity with a savepoint
            with self.env.cr.savepoint():
                # Double-check that message_follower_ids is removed
                lead_vals.pop('message_follower_ids', None)

                # Get CRM fields to check if all fields exist
                crm_fields = self.env['crm.lead'].sudo().fields_get()

                # Remove any fields that don't exist in the CRM model
                fields_to_remove = []
                for field in lead_vals.keys():
                    if field not in crm_fields:
                        fields_to_remove.append(field)

                for field in fields_to_remove:
                    _logger.warning(f"Removing field '{field}' from lead_vals as it doesn't exist in CRM model")
                    lead_vals.pop(field, None)

                # Create the lead with context to prevent follower creation
                lead = self.env['crm.lead'].sudo().with_context(**MailCtx).create(lead_vals)
                _logger.info("LEAD CREATED id=%s with reference number %s", lead.id, lead_vals.get('website_ref_number', 'N/A'))
                vals['lead_id'] = lead.id

                # Set company_id from the created lead's policy provider
                if lead.policy_provider_id:
                    vals['company_id'] = lead.policy_provider_id.id
                    _logger.info(f"Set company_id to {lead.policy_provider_id.name} from CRM lead")

                # Subscribe partner manually (only once)
                if vals.get('partner_id'):
                    lead.message_subscribe(partner_ids=[vals['partner_id']])
                    _logger.info("Manually subscribed partner %s to lead %s", vals['partner_id'], lead.id)

                # Log the followers for debugging
                _logger.debug("LEAD FOLLOWERS AFTER CREATE: %r", lead.message_follower_ids.mapped('partner_id.id'))

                # Try to link the premium car form ID to the lead if the field exists
                crm_fields = self.env['crm.lead'].sudo().fields_get()
                if 'premium_car_form_id' in crm_fields and hasattr(lead, 'premium_car_form_id'):
                    lead.sudo().write({'premium_car_form_id': 0})  # Will be replaced with actual ID after creation
                    _logger.debug("premium_car_form_id field exists in CRM lead model")

        # Process each record in vals_list and add to new_vals_list
        for vals in vals_list:
            new_vals_list.append(vals)

        # Ensure required driver fields are not null
        for vals in new_vals_list:
            # If no driver1_name provided, default to empty string
            if not vals.get('driver1_name'):
                vals['driver1_name'] = ''

            # Handle additional driver fields
            if vals.get('has_additional_driver') == 'yes':
                if not vals.get('driver2_name'):
                    vals['driver2_name'] = ''

                if vals.get('has_driver3') == 'yes':
                    if not vals.get('driver3_name'):
                        vals['driver3_name'] = ''

                    if vals.get('has_driver4') == 'yes':
                        if not vals.get('driver4_name'):
                            vals['driver4_name'] = ''

        # Create the records with a simple super call
        _logger.info("Calling super().create with %d records", len(new_vals_list))
        records = super(PremiumCarEndkunden, self).create(new_vals_list)
        _logger.info("Successfully created %d records", len(records))

        # Update the CRM leads with the premium_car_form_id if the field exists
        for record in records:
            if record.lead_id:
                # Use a savepoint for updating the lead
                with self.env.cr.savepoint():
                    # Check if the premium_car_form_id field exists in the CRM lead model
                    crm_fields = self.env['crm.lead'].sudo().fields_get()
                    if 'premium_car_form_id' in crm_fields and hasattr(record.lead_id, 'premium_car_form_id'):
                        record.lead_id.sudo().write({
                            'premium_car_form_id': record.id
                        })
                        _logger.info("Updated CRM lead %s with premium_car_form_id: %s", record.lead_id.id, record.id)

        # Log completion
        _logger.info("========== PREMIUM CAR ENDKUNDEN CREATE COMPLETE ==========")

        return records

    def action_view_lead(self):
        """Open the CRM lead form view"""
        self.ensure_one()
        if not self.lead_id:
            return

        return {
            'name': 'CRM Lead',
            'type': 'ir.actions.act_window',
            'res_model': 'crm.lead',
            'res_id': self.lead_id.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def generate_pdf_summary(self):
        """Generate a PDF summary of the Premium Car Endkunden request"""
        self.ensure_one()

        if not REPORTLAB_AVAILABLE:
            _logger.error("ReportLab is not available. Cannot generate PDF.")
            raise UserError(_("PDF generation is not available. Please contact your administrator."))

        try:
            # Create a buffer to hold the PDF
            buffer = io.BytesIO()

            # Create the PDF document with custom page template for logo support
            doc = BaseDocTemplate(buffer, pagesize=A4, leftMargin=0.75*inch, rightMargin=0.75*inch)

            # Create a frame for the content
            frame = Frame(0.75*inch, 0.75*inch, A4[0] - 1.5*inch, A4[1] - 1.5*inch,
                         leftPadding=0, bottomPadding=0, rightPadding=0, topPadding=0)

            # Create page template with header/footer function
            page_template = PageTemplate(id='main', frames=[frame], onPage=self._create_header_footer)
            doc.addPageTemplates([page_template])

            story = []

            # Get styles
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                textColor=colors.darkred,
                alignment=1  # Center alignment
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                textColor=colors.darkblue
            )

            normal_style = styles['Normal']

            # Create a style for table labels to enable text wrapping
            label_style = ParagraphStyle(
                'LabelStyle',
                parent=styles['Normal'],
                fontSize=10,
                fontName='Helvetica-Bold',
                leading=12
            )

            value_style = ParagraphStyle(
                'ValueStyle',
                parent=styles['Normal'],
                fontSize=10,
                fontName='Helvetica',
                leading=12
            )

            # Add large spacer to push title below logo and improve page breaks
            story.append(Spacer(1, 80))

            # Title
            story.append(Paragraph("Premium Car Erstanfrage Endkunden - Zusammenfassung", title_style))
            story.append(Spacer(1, 20))

            # Reference number
            story.append(Paragraph(f"<b>Referenznummer:</b> {self.name}", normal_style))
            story.append(Spacer(1, 20))

            # Personal Information Section
            story.append(Paragraph("Persönliche Daten", heading_style))

            personal_data = [
                [Paragraph('Anrede:', label_style), Paragraph(self._get_salutation_display(), value_style)],
                [Paragraph('Vorname:', label_style), Paragraph(self.first_name or '', value_style)],
                [Paragraph('Nachname:', label_style), Paragraph(self.last_name or '', value_style)],
                [Paragraph('Versicherungsbeginn:', label_style), Paragraph(self.insurance_start_date.strftime('%d.%m.%Y') if self.insurance_start_date else '', value_style)],
            ]

            personal_table = Table(personal_data, colWidths=[3.2*inch, 3.3*inch])
            personal_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(personal_table)
            story.append(Spacer(1, 20))

            # Address Information Section
            story.append(Paragraph("Adresse", heading_style))

            address_data = [
                [Paragraph('Straße:', label_style), Paragraph(f"{self.street or ''} {self.street_number or ''}".strip(), value_style)],
                [Paragraph('Adresszusatz:', label_style), Paragraph(self.street_addition or '', value_style)],
                [Paragraph('PLZ:', label_style), Paragraph(self.zip or '', value_style)],
                [Paragraph('Ort:', label_style), Paragraph(self.city or '', value_style)],
                [Paragraph('Land:', label_style), Paragraph(self.country_id.name if self.country_id else '', value_style)],
            ]

            address_table = Table(address_data, colWidths=[3.2*inch, 3.3*inch])
            address_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(address_table)
            story.append(Spacer(1, 20))

            # Contact Information Section
            story.append(Paragraph("Kontaktdaten", heading_style))

            contact_data = [
                [Paragraph('E-Mail:', label_style), Paragraph(self.email or '', value_style)],
                [Paragraph('Telefonnummer:', label_style), Paragraph(self._format_phone_number(self.phone), value_style)],
                [Paragraph('Mobilfunknummer:', label_style), Paragraph(self._format_phone_number(self.mobile), value_style)],
            ]

            contact_table = Table(contact_data, colWidths=[3.2*inch, 3.3*inch])
            contact_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(contact_table)
            story.append(Spacer(1, 20))

            # Owner Information Section (only if different from policyholder)
            if self.is_owner_different == 'yes':
                story.append(Paragraph("Halter-Informationen", heading_style))

                owner_data = [
                    [Paragraph('Anrede (Halter):', label_style), Paragraph(self._get_owner_salutation_display(), value_style)],
                    [Paragraph('Vorname (Halter):', label_style), Paragraph(self.owner_first_name or '', value_style)],
                    [Paragraph('Nachname (Halter):', label_style), Paragraph(self.owner_last_name or '', value_style)],
                    [Paragraph('Straße (Halter):', label_style), Paragraph(f"{self.owner_street or ''} {self.owner_street_number or ''}".strip(), value_style)],
                    [Paragraph('Adresszusatz (Halter):', label_style), Paragraph(self.owner_street_addition or '', value_style)],
                    [Paragraph('PLZ (Halter):', label_style), Paragraph(self.owner_zip or '', value_style)],
                    [Paragraph('Ort (Halter):', label_style), Paragraph(self.owner_city or '', value_style)],
                    [Paragraph('Land (Halter):', label_style), Paragraph(self.owner_country_id.name if self.owner_country_id else '', value_style)],
                    [Paragraph('E-Mail (Halter):', label_style), Paragraph(self.owner_email or '', value_style)],
                    [Paragraph('Telefonnummer (Halter):', label_style), Paragraph(self._format_phone_number(self.owner_phone), value_style)],
                    [Paragraph('Mobilfunknummer (Halter):', label_style), Paragraph(self._format_phone_number(self.owner_mobile), value_style)],
                ]

                owner_table = Table(owner_data, colWidths=[3.2*inch, 3.3*inch])
                owner_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTSIZE', (0, 0), (-1, -1), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                    ('TOPPADDING', (0, 0), (-1, -1), 6),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                    ('LEFTPADDING', (0, 0), (-1, -1), 0),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 8),
                ]))

                story.append(owner_table)
                story.append(Spacer(1, 20))

            # Vehicle Information Section
            story.append(Paragraph("Fahrzeuginformationen", heading_style))

            vehicle_data = [
                [Paragraph('Fahrzeughersteller:', label_style), Paragraph(self.car_manufacturer_id.name if self.car_manufacturer_id else '', value_style)],
                [Paragraph('Fahrzeugtyp:', label_style), Paragraph(self.car_type or '', value_style)],
                [Paragraph('Amtliches Kennzeichen:', label_style), Paragraph(self.license_plate or '', value_style)],
                [Paragraph('Erstzulassungsdatum:', label_style), Paragraph(self.first_registration_date.strftime('%d.%m.%Y') if self.first_registration_date else '', value_style)],
                [Paragraph('Zulassungsdatum auf Versicherungsnehmer:', label_style), Paragraph(self.owner_registration_date.strftime('%d.%m.%Y') if self.owner_registration_date else '', value_style)],
                [Paragraph('Aktueller km-Stand:', label_style), Paragraph(f"{self.current_mileage:,}" if self.current_mileage else '', value_style)],
                [Paragraph('Fahrzeugwert Brutto:', label_style), Paragraph(f"{self.vehicle_value:,.2f} €" if self.vehicle_value else '', value_style)],
                [Paragraph('Saison-Zulassung:', label_style), Paragraph(self._get_is_season_registration_display(), value_style)],
                [Paragraph('Vorsteuerabzugsberechtigung:', label_style), Paragraph(self._get_has_vat_deduction_display(), value_style)],
                [Paragraph('Zahlart des Fahrzeuges:', label_style), Paragraph(self._get_payment_type_display(), value_style)],
            ]

            # Add seasonal period if applicable
            if self.is_season_registration == 'yes':
                if self.seasonal_period_from:
                    vehicle_data.append([Paragraph('Saisonzeitraum von:', label_style), Paragraph(self.seasonal_period_from.strftime('%d.%m.%Y'), value_style)])
                if self.seasonal_period_to:
                    vehicle_data.append([Paragraph('Saisonzeitraum bis:', label_style), Paragraph(self.seasonal_period_to.strftime('%d.%m.%Y'), value_style)])

            vehicle_table = Table(vehicle_data, colWidths=[3.2*inch, 3.3*inch])
            vehicle_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(vehicle_table)
            story.append(Spacer(1, 20))

            # Continue with more sections in the next part...
            return self._continue_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

        except Exception as e:
            _logger.error(f"Error generating PDF: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            raise UserError(_("Error generating PDF. Please try again or contact support."))

    def _get_logo_path(self):
        """Get the path to the logo file for PDF header"""
        import os

        try:
            _logger.info("=== GETTING LOGO PATH ===")

            # List of possible logo paths to try
            possible_paths = []

            # Method 1: Using __file__ to get current module directory
            try:
                current_dir = os.path.dirname(os.path.abspath(__file__))
                _logger.info(f"Current module directory: {current_dir}")

                # Go up to InsuranceForm directory, then to static/img
                insurance_form_dir = os.path.dirname(current_dir)  # Go up from models/ to InsuranceForm/
                possible_paths.append(os.path.join(insurance_form_dir, 'static', 'img', 'focus_logo.png'))

            except Exception as e:
                _logger.error(f"Error getting current directory: {e}")

            # Method 2: Using Odoo's get_module_path if available
            try:
                from odoo.modules import get_module_path
                module_path_odoo = get_module_path('InsuranceForm')
                if module_path_odoo:
                    possible_paths.append(os.path.join(module_path_odoo, 'static', 'img', 'focus_logo.png'))
            except Exception as e:
                _logger.error(f"Error getting module path via Odoo: {e}")

            # Method 3: Try relative paths from common Odoo locations
            try:
                import odoo
                odoo_dir = os.path.dirname(odoo.__file__)
                addons_paths = [
                    os.path.join(odoo_dir, '..', 'addons', 'InsuranceForm', 'static', 'img', 'focus_logo.png'),
                    os.path.join(odoo_dir, 'addons', 'InsuranceForm', 'static', 'img', 'focus_logo.png'),
                ]
                possible_paths.extend(addons_paths)
            except Exception as e:
                _logger.error(f"Error getting Odoo addons paths: {e}")

            _logger.info(f"Possible logo paths to check: {possible_paths}")

            # Check each path and return the first one that exists
            for path in possible_paths:
                abs_path = os.path.abspath(path)
                _logger.info(f"Checking logo path: {abs_path}")
                if os.path.exists(abs_path):
                    _logger.info(f"Found logo at: {abs_path}")
                    return abs_path
                else:
                    _logger.info(f"Logo not found at: {abs_path}")

            _logger.warning("Logo file not found in any of the expected locations")
            return None

        except Exception as e:
            _logger.error(f"Error getting logo path: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def _create_header_footer(self, canvas, doc):
        """Create header with logo and footer for PDF pages"""
        import os
        try:
            _logger.info("=== HEADER FUNCTION CALLED ===")

            # Get logo path
            logo_path = self._get_logo_path()

            if logo_path and os.path.exists(logo_path):
                _logger.info(f"Attempting to draw logo from: {logo_path}")

                # Draw logo in top-right corner with professional sizing
                # Position: 0.4 inch from right edge, 0.25 inch from top (reverted to original position)
                # Title will be positioned below the logo for better visual hierarchy
                logo_width = 2.0 * inch   # Increased from 1.2 to 2.0 inches (67% larger)
                logo_height = 1.0 * inch  # Increased from 0.6 to 1.0 inches (67% larger)

                x_position = A4[0] - 0.4*inch - logo_width  # Right edge minus padding minus logo width
                y_position = A4[1] - 0.25*inch - logo_height  # Top edge minus padding minus logo height (reverted to original)

                _logger.info(f"Drawing logo at position: x={x_position}, y={y_position}, width={logo_width}, height={logo_height}")

                canvas.drawImage(logo_path, x_position, y_position,
                               width=logo_width, height=logo_height,
                               preserveAspectRatio=True, mask='auto')

                _logger.info("Logo successfully drawn to PDF")
            else:
                _logger.info("No valid logo file found, skipping logo in PDF header")

        except Exception as e:
            # Log error but don't fail PDF generation
            _logger.error(f"Error adding logo to PDF: {e}")
            _logger.error(f"Traceback: {traceback.format_exc()}")
            pass

    def _continue_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Continue PDF generation with remaining sections"""

        # Driver Information Section
        story.append(Paragraph("Fahrerinformationen", heading_style))

        driver_data = [
            [Paragraph('Fahrer 1 Name:', label_style), Paragraph(self.driver1_name or '', value_style)],
            [Paragraph('Fahrer 1 Geburtsdatum:', label_style), Paragraph(self.driver1_birthdate.strftime('%d.%m.%Y') if self.driver1_birthdate else '', value_style)],
        ]

        # Add additional drivers if they exist
        if self.has_additional_driver == 'yes' and self.driver2_name:
            driver_data.extend([
                [Paragraph('Fahrer 2 Name:', label_style), Paragraph(self.driver2_name, value_style)],
                [Paragraph('Fahrer 2 Geburtsdatum:', label_style), Paragraph(self.driver2_birthdate.strftime('%d.%m.%Y') if self.driver2_birthdate else '', value_style)],
            ])

        if self.has_driver3 == 'yes' and self.driver3_name:
            driver_data.extend([
                [Paragraph('Fahrer 3 Name:', label_style), Paragraph(self.driver3_name, value_style)],
                [Paragraph('Fahrer 3 Geburtsdatum:', label_style), Paragraph(self.driver3_birthdate.strftime('%d.%m.%Y') if self.driver3_birthdate else '', value_style)],
            ])

        if self.has_driver4 == 'yes' and self.driver4_name:
            driver_data.extend([
                [Paragraph('Fahrer 4 Name:', label_style), Paragraph(self.driver4_name, value_style)],
                [Paragraph('Fahrer 4 Geburtsdatum:', label_style), Paragraph(self.driver4_birthdate.strftime('%d.%m.%Y') if self.driver4_birthdate else '', value_style)],
            ])

        driver_table = Table(driver_data, colWidths=[3.2*inch, 3.3*inch])
        driver_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(driver_table)
        story.append(Spacer(1, 20))

        # Usage and Risk Information Section
        story.append(Paragraph("Nutzung und Risikoinformationen", heading_style))

        usage_data = [
            [Paragraph('Verwendungszweck:', label_style), Paragraph(self._get_usage_purpose_display(), value_style)],
            [Paragraph('Jahresfahrleistung:', label_style), Paragraph(f"{self.annual_mileage:,} km" if self.annual_mileage else '', value_style)],
            [Paragraph('Tracker vorhanden:', label_style), Paragraph(self._get_has_tracker_display(), value_style)],
        ]

        # Only add daily vehicle license if it exists
        if self.daily_vehicle_license:
            usage_data.append([Paragraph('Kennzeichen Alltagsfahrzeug:', label_style), Paragraph(self.daily_vehicle_license, value_style)])

        usage_data.extend([
            [Paragraph('Fahrzeugstandort bei Versicherungsnehmer:', label_style), Paragraph(self._get_is_vehicle_at_owner_address_display(), value_style)],
            [Paragraph('Risikoannahmevoraussetzung:', label_style), Paragraph(self._get_risk_requirement_display(), value_style)],
        ])

        usage_table = Table(usage_data, colWidths=[3.2*inch, 3.3*inch])
        usage_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(usage_table)
        story.append(Spacer(1, 20))

        # Vehicle Location Section (only if different from owner address)
        if self.is_vehicle_at_owner_address == 'no':
            story.append(Paragraph("Fahrzeugstandort", heading_style))

            location_data = [
                [Paragraph('Straße (Fahrzeugstandort):', label_style), Paragraph(f"{self.vehicle_street or ''} {self.vehicle_street_number or ''}".strip(), value_style)],
                [Paragraph('Adresszusatz (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_street_addition or '', value_style)],
                [Paragraph('PLZ (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_zip or '', value_style)],
                [Paragraph('Ort (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_city or '', value_style)],
                [Paragraph('Land (Fahrzeugstandort):', label_style), Paragraph(self.vehicle_country_id.name if self.vehicle_country_id else '', value_style)],
            ]

            location_table = Table(location_data, colWidths=[3.2*inch, 3.3*inch])
            location_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(location_table)
            story.append(Spacer(1, 20))

        # Security Systems Section
        story.append(Paragraph("Sicherheitssysteme", heading_style))

        security_data = [
            [Paragraph('Einbruchmeldeanlage vorhanden:', label_style), Paragraph(self._get_has_burglar_alarm_display(), value_style)],
            [Paragraph('Brandmeldeanlage vorhanden:', label_style), Paragraph(self._get_has_fire_alarm_display(), value_style)],
        ]

        # Add connection details if alarms are present
        if self.has_burglar_alarm == 'yes' and self.has_alarm_police_connection:
            security_data.append([Paragraph('Aufschaltung Einbruchmeldeanlage:', label_style), Paragraph(self._get_has_alarm_police_connection_display(), value_style)])

        if self.has_fire_alarm == 'yes' and self.has_fire_department_connection:
            security_data.append([Paragraph('Aufschaltung Brandmeldeanlage:', label_style), Paragraph(self._get_has_fire_department_connection_display(), value_style)])

        security_table = Table(security_data, colWidths=[3.2*inch, 3.3*inch])
        security_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(security_table)
        story.append(Spacer(1, 20))

        # Continue with more sections...
        return self._finalize_pdf_generation(story, doc, buffer, heading_style, normal_style, label_style, value_style)

    def _finalize_pdf_generation(self, story, doc, buffer, heading_style, normal_style, label_style, value_style):
        """Finalize PDF generation with remaining sections"""

        # Previous Insurance Section (only if applicable)
        if self.has_previous_insurance == 'yes':
            story.append(Paragraph("Vorversicherung", heading_style))

            insurance_data = [
                [Paragraph('Vorversicherung vorhanden:', label_style), Paragraph(self._get_has_previous_insurance_display(), value_style)],
            ]

            if self.previous_insurance_details:
                insurance_data.append([Paragraph('Details der Vorversicherung:', label_style), Paragraph(self.previous_insurance_details, value_style)])

            insurance_table = Table(insurance_data, colWidths=[3.2*inch, 3.3*inch])
            insurance_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(insurance_table)
            story.append(Spacer(1, 20))

        # Previous Damage Section (only if applicable)
        if self.has_previous_damage == 'yes':
            story.append(Paragraph("Vorschäden", heading_style))

            damage_data = [
                [Paragraph('Vorschäden vorhanden:', label_style), Paragraph(self._get_has_previous_damage_display(), value_style)],
            ]

            if self.liability_damage_count is not None and self.liability_damage_count > 0:
                damage_data.append([Paragraph('Anzahl Vorschäden Haftpflicht:', label_style), Paragraph(str(self.liability_damage_count), value_style)])
            if self.full_coverage_damage_count is not None and self.full_coverage_damage_count > 0:
                damage_data.append([Paragraph('Anzahl Vorschäden Vollkasko:', label_style), Paragraph(str(self.full_coverage_damage_count), value_style)])
            if self.partial_coverage_damage_count is not None and self.partial_coverage_damage_count > 0:
                damage_data.append([Paragraph('Anzahl Vorschäden Teilkasko:', label_style), Paragraph(str(self.partial_coverage_damage_count), value_style)])

            damage_table = Table(damage_data, colWidths=[3.2*inch, 3.3*inch])
            damage_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(damage_table)
            story.append(Spacer(1, 20))

        # Similar Vehicles Section (only if applicable)
        if self.has_similar_vehicles == 'yes':
            story.append(Paragraph("Vergleichbare Fahrzeuge", heading_style))

            similar_data = [
                [Paragraph('Weitere vergleichbare Fahrzeuge:', label_style), Paragraph(self._get_has_similar_vehicles_display(), value_style)],
            ]

            if self.similar_vehicles_description:
                similar_data.append([Paragraph('Beschreibung:', label_style), Paragraph(self.similar_vehicles_description, value_style)])

            similar_table = Table(similar_data, colWidths=[3.2*inch, 3.3*inch])
            similar_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(similar_table)
            story.append(Spacer(1, 20))

        # Previous Experience Section
        story.append(Paragraph("Fahrerfahrung", heading_style))

        experience_data = [
            [Paragraph('Vorerfahrung auf vergleichbaren Fahrzeugen:', label_style), Paragraph(self._get_has_previous_experience_display(), value_style)],
        ]

        experience_table = Table(experience_data, colWidths=[3.2*inch, 3.3*inch])
        experience_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 0),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ]))

        story.append(experience_table)
        story.append(Spacer(1, 20))

        # Additional Notes Section (only if provided)
        if self.notes and self.notes.strip() and self.notes.strip().lower() not in ['no', 'nein', '']:
            story.append(Paragraph("Weitere Mitteilungen", heading_style))

            # Create a table for better formatting
            notes_data = [[Paragraph('Mitteilungen:', label_style), Paragraph(self.notes, value_style)]]
            notes_table = Table(notes_data, colWidths=[3.2*inch, 3.3*inch])
            notes_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(notes_table)
            story.append(Spacer(1, 20))

        # Uploaded Documents Section (only if any documents were uploaded)
        uploaded_docs = []
        if self.value_proof and self.value_proof_filename:
            uploaded_docs.append(['Wertnachweis:', self.value_proof_filename])
        if self.vehicle_documents and self.vehicle_documents_filename:
            uploaded_docs.append(['Fahrzeugunterlagen:', self.vehicle_documents_filename])
        if self.photos and self.photos_filename:
            uploaded_docs.append(['Fotos:', self.photos_filename])

        if uploaded_docs:
            story.append(Paragraph("Hochgeladene Dokumente", heading_style))

            docs_data = [[Paragraph(label, label_style), Paragraph(filename, value_style)] for label, filename in uploaded_docs]
            docs_table = Table(docs_data, colWidths=[3.2*inch, 3.3*inch])
            docs_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('LEFTPADDING', (0, 0), (-1, -1), 0),
                ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ]))

            story.append(docs_table)
            story.append(Spacer(1, 20))

        # Footer
        story.append(Spacer(1, 30))
        footer_text = f"Erstellt am: {fields.Datetime.now().strftime('%d.%m.%Y %H:%M')} Uhr"
        story.append(Paragraph(footer_text, normal_style))

        # Build PDF
        doc.build(story)

        # Get the PDF data
        pdf_data = buffer.getvalue()
        buffer.close()

        return base64.b64encode(pdf_data)

    def _format_phone_number(self, phone_number):
        """Format phone number to ensure it displays with country code prefix"""
        if not phone_number:
            return ''

        # If the phone number doesn't start with +, add it
        # (since our controller stores them as international numbers)
        if phone_number and not phone_number.startswith('+'):
            return f'+{phone_number}'

        return phone_number

    def _get_salutation_display(self):
        """Get display value for salutation"""
        salutation_dict = dict(self._fields['salutation'].selection)
        return salutation_dict.get(self.salutation, '')

    def _get_owner_salutation_display(self):
        """Get display value for owner salutation"""
        if not self.owner_salutation:
            return ''
        salutation_dict = dict(self._fields['owner_salutation'].selection)
        return salutation_dict.get(self.owner_salutation, '')

    def _get_is_season_registration_display(self):
        """Get display value for season registration"""
        season_dict = dict(self._fields['is_season_registration'].selection)
        return season_dict.get(self.is_season_registration, '')

    def _get_has_vat_deduction_display(self):
        """Get display value for VAT deduction"""
        vat_dict = dict(self._fields['has_vat_deduction'].selection)
        return vat_dict.get(self.has_vat_deduction, '')

    def _get_payment_type_display(self):
        """Get display value for payment type"""
        payment_dict = dict(self._fields['payment_type'].selection)
        return payment_dict.get(self.payment_type, '')

    def _get_usage_purpose_display(self):
        """Get display value for usage purpose"""
        usage_dict = dict(self._fields['usage_purpose'].selection)
        return usage_dict.get(self.usage_purpose, '')

    def _get_has_tracker_display(self):
        """Get display value for tracker"""
        tracker_dict = dict(self._fields['has_tracker'].selection)
        return tracker_dict.get(self.has_tracker, '')

    def _get_is_vehicle_at_owner_address_display(self):
        """Get display value for vehicle at owner address"""
        address_dict = dict(self._fields['is_vehicle_at_owner_address'].selection)
        return address_dict.get(self.is_vehicle_at_owner_address, '')

    def _get_risk_requirement_display(self):
        """Get display value for risk requirement"""
        risk_dict = dict(self._fields['risk_requirement'].selection)
        return risk_dict.get(self.risk_requirement, '')

    def _get_has_burglar_alarm_display(self):
        """Get display value for burglar alarm"""
        alarm_dict = dict(self._fields['has_burglar_alarm'].selection)
        return alarm_dict.get(self.has_burglar_alarm, '')

    def _get_has_fire_alarm_display(self):
        """Get display value for fire alarm"""
        fire_dict = dict(self._fields['has_fire_alarm'].selection)
        return fire_dict.get(self.has_fire_alarm, '')

    def _get_has_alarm_police_connection_display(self):
        """Get display value for alarm police connection"""
        if not self.has_alarm_police_connection:
            return ''
        connection_dict = dict(self._fields['has_alarm_police_connection'].selection)
        return connection_dict.get(self.has_alarm_police_connection, '')

    def _get_has_fire_department_connection_display(self):
        """Get display value for fire department connection"""
        if not self.has_fire_department_connection:
            return ''
        connection_dict = dict(self._fields['has_fire_department_connection'].selection)
        return connection_dict.get(self.has_fire_department_connection, '')

    def _get_has_previous_insurance_display(self):
        """Get display value for previous insurance"""
        insurance_dict = dict(self._fields['has_previous_insurance'].selection)
        return insurance_dict.get(self.has_previous_insurance, '')

    def _get_has_previous_damage_display(self):
        """Get display value for previous damage"""
        damage_dict = dict(self._fields['has_previous_damage'].selection)
        return damage_dict.get(self.has_previous_damage, '')

    def _get_has_similar_vehicles_display(self):
        """Get display value for similar vehicles"""
        similar_dict = dict(self._fields['has_similar_vehicles'].selection)
        return similar_dict.get(self.has_similar_vehicles, '')

    def _get_has_previous_experience_display(self):
        """Get display value for previous experience"""
        experience_dict = dict(self._fields['has_previous_experience'].selection)
        return experience_dict.get(self.has_previous_experience, '')

class VehicleManufacturer(models.Model):
    _name = 'vehicle.manufacturer'
    _description = 'Vehicle Manufacturer'
    _order = 'name'

    name = fields.Char('Name', required=True)
    active = fields.Boolean('Active', default=True)