#!/usr/bin/env python3
"""
Test script to verify company-based access control is working properly
"""

import xmlrpc.client
import sys

# Configuration
URL = "https://erp-lab1.focuskg.de"
DB = "wvm-vermoegensverwaltung"
USERNAME = "admin"  # Change this to your username
PASSWORD = "admin"  # Change this to your password

def test_company_access():
    """Test company-based access control for insurance forms"""
    
    print("=== Testing Company-Based Access Control ===")
    
    try:
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f"{URL}/xmlrpc/2/common")
        uid = common.authenticate(DB, USERNAME, PASSWORD, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return False
            
        print(f"✅ Connected as user ID: {uid}")
        models = xmlrpc.client.ServerProxy(f"{URL}/xmlrpc/2/object")
        
        # Get current user info
        user_info = models.execute_kw(
            DB, uid, PASSWORD,
            'res.users', 'read',
            [uid], {'fields': ['name', 'company_id', 'company_ids']}
        )
        
        if user_info:
            user = user_info[0]
            print(f"User: {user['name']}")
            print(f"Current Company: {user['company_id']}")
            print(f"Accessible Companies: {user['company_ids']}")
        
        # Test each insurance model
        models_to_test = [
            'premium.car.request',
            'premiumcar.untervermittler', 
            'premiumcar.endkunden',
            'premiumcar.erstanfrage.endkunden.intern',
            'augencheck.request'
        ]
        
        for model_name in models_to_test:
            print(f"\n--- Testing {model_name} ---")
            
            try:
                # Get all records (should be filtered by company)
                all_records = models.execute_kw(
                    DB, uid, PASSWORD,
                    model_name, 'search_read',
                    [[]], {'fields': ['id', 'name', 'company_id'], 'limit': 10}
                )
                
                print(f"Found {len(all_records)} records")
                
                # Check company_id values
                companies_found = set()
                records_without_company = 0
                
                for record in all_records:
                    if record.get('company_id'):
                        companies_found.add(record['company_id'][1])  # company name
                    else:
                        records_without_company += 1
                        print(f"⚠️  Record {record['id']} has no company_id!")
                
                if companies_found:
                    print(f"Companies found in records: {list(companies_found)}")
                
                if records_without_company > 0:
                    print(f"❌ {records_without_company} records without company_id")
                else:
                    print("✅ All records have company_id set")
                    
            except Exception as e:
                print(f"❌ Error testing {model_name}: {e}")
        
        # Test debug method
        print(f"\n--- Testing Debug Method ---")
        try:
            debug_result = models.execute_kw(
                DB, uid, PASSWORD,
                'premium.car.request', 'debug_company_access',
                []
            )
            print(f"Debug result: {debug_result}")
        except Exception as e:
            print(f"❌ Error calling debug method: {e}")
        
        print("\n=== Test Complete ===")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_company_access()
    sys.exit(0 if success else 1)
