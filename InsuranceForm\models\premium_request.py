from odoo import models, fields, api, _
import logging

_logger = logging.getLogger(__name__)

class PremiumCarRequest(models.Model):
    _inherit = 'premium.car.request'
    _description = 'Premium Car Insurance Initial Request'
    _inherit += ['mail.thread', 'mail.activity.mixin']

    # Broker Information
    broker_name = fields.Char(string="Broker Name")
    email = fields.Char(string="Email")

    # Vehicle information
    vehicle_manufacturer = fields.Many2one('vehicle.manufacturer', string="Fahrzeughersteller")
    vehicle_type = fields.Char(string="Fahrzeugtyp")
    car_manufacturer_id = fields.Many2one('vehicle.manufacturer', string="Car Manufacturer")
    car_make = fields.Char(string="Car Make")
    car_model = fields.Char(string="Car Model")
    car_year = fields.Integer(string="Year")
    license_plate = fields.Char(string="Kennzeichen")
    coverage_type = fields.Selection([
        ('liability', 'Liability Only'),
        ('partial', 'Partial Coverage'),
        ('full', 'Full Coverage')
    ], string="Coverage Type")
    vehicle_value = fields.Float(string="Fahrzeugwert")
    first_registration = fields.Date(string="Erstzulassung")
    registration_on_policyholder = fields.Date(string="Zulassung auf VN")
    current_milage = fields.Integer(string="Aktueller km-Stand")
    annual_milage = fields.Integer(string="Jahresfahrleistung")
    current_mileage = fields.Integer(string="Current Mileage")
    annual_mileage = fields.Integer(string="Annual Mileage")

    # Insurance information
    insurance_start_date = fields.Date(string="Versicherungsbeginn")
    seasonal_registration = fields.Boolean(string="Saisonzulassung")
    seasonal_period_from = fields.Date(string="Saisonzeitraum von")
    seasonal_period_to = fields.Date(string="Saisonzeitraum bis")
    input_tax_deduction_entitlement = fields.Boolean(string="Vorsteuerabzugsberechtigung")
    payment_type_for_vehicle = fields.Selection([
        ('leasing', 'Leasing'),
        ('financing', 'Finanzierung'),
        ('cash_payment', 'Barzahlung')
    ], string="Zahlart")
    usage_purpose = fields.Selection([
        ('mainly_private', 'Überwiegend privat'),
        ('mainly_professional', 'Überwiegend beruflich'),
        ('only_private', 'Nur privat'),
        ('only_professional', 'Nur beruflich')
    ], string="Verwendungszweck")
    existing_insurance = fields.Boolean(string="Vorversicherung")
    previous_insurer = fields.Char(string="Previous Insurer")
    previous_insurer_and_policy_number = fields.Char(string="Vorversicherer und Policen-Nr")
    previous_claims = fields.Boolean(string="Vorschäden vorhanden")
    no_claim_years = fields.Integer(string="No-Claim Bonus (Years)")

    # Driver information
    driver_1 = fields.Char(string="Fahrer 1")
    dob_driver_1 = fields.Date(string="Geburtsdatum Fahrer 1")
    driver_2 = fields.Char(string="Fahrer 2")
    dob_driver_2 = fields.Date(string="Geburtsdatum Fahrer 2")
    driver_3 = fields.Char(string="Fahrer 3")
    dob_driver_3 = fields.Date(string="Geburtsdatum Fahrer 3")
    driver_4 = fields.Char(string="Fahrer 4")
    dob_driver_4 = fields.Date(string="Geburtsdatum Fahrer 4")

    driver1_name = fields.Char(string="Driver 1 Name")
    driver1_birthdate = fields.Date(string="Driver 1 Birth Date")
    driver2_name = fields.Char(string="Driver 2 Name")
    driver2_birthdate = fields.Date(string="Driver 2 Birth Date")

    # Boolean fields with selection for proper mapping
    is_season_registration = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Seasonal Registration")

    # Security and risk fields
    tracker = fields.Boolean(string="Tracker vorhanden")
    has_tracker = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Has Tracker")

    has_previous_insurance = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Has Previous Insurance")

    has_previous_damage = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Has Previous Damage")

    # Security features
    burglary_alarm_system = fields.Selection([
        ('no_burglary_alarm_system', 'Keine Einbruchmeldeanlage'),
        ('burglary_alarm_system', 'Einbruchmeldeanlage vorhanden'),
        ('connected_burglary_alarm_system', 'Aufgeschaltete Einbruchmeldeanlage')
    ], string="Einbruchmeldeanlage")

    fire_alarm_system = fields.Selection([
        ('no_fire_alarm_system', 'Keine Brandmeldeanlage'),
        ('fire_alarm_system', 'Brandmeldeanlage vorhanden'),
        ('connected_fire_alarm_system', 'Aufgeschaltete Brandmeldeanlage')
    ], string="Brandmeldeanlage")

    has_burglar_alarm = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Has Burglar Alarm")

    has_alarm_police_connection = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Has Police Connection")

    has_fire_alarm = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Has Fire Alarm")

    has_fire_department_connection = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Has Fire Department Connection")

    risk_requirement = fields.Selection([
        ('single_garage', 'Einzel-/Doppelgarage'),
        ('communal_garage', 'Sammelgarage/Halle'),
        ('no_garage', 'keine abschließbare, überdachte und durch feste Wände umschlossene Abstellmöglichkeit')
    ], string="Risikoannahmevoraussetzung")

    # Vehicle owner information
    divergent_vehicle_owner = fields.Boolean(string="Halter abweichend vom VN")
    owning_comparable_vehicles = fields.Boolean(string="Besitz vergleichbarer Fahrzeuge")
    comparable_vehicles = fields.Char(string="Comparable Vehicles")
    driving_experience_with_comparable_vehicle = fields.Boolean(string="Fahrerfahrung mit vergleichbaren Fahrzeugen")
    main_vehicle_location_at_policyholder_address = fields.Boolean(string="Fahrzeugstandort bei VN")
    main_vehicle_location = fields.Char(string="Main Vehicle Location")
    license_plate_of_daily_use_vehicle = fields.Char(string="Kennzeichen Alltagsfahrzeug")

    # Additional information
    other_information = fields.Text(string="Weitere Informationen")

    # Claims details
    number_of_claim_in_motor_liability_insurance = fields.Integer(string="Anzahl Schäden in KFZ-Haftpflicht")
    description_of_claim_in_motor_liability_insurance = fields.Text(string="Beschreibung der Schäden in KFZ-Haftpflicht")
    number_of_claims_in_comprehensive_insurance = fields.Integer(string="Anzahl Schäden in Vollkasko")
    description_of_claims_in_comprehensive_insurance = fields.Text(string="Beschreibung der Schäden in Vollkasko")
    number_of_claims_in_partial_coverage_insurance = fields.Integer(string="Anzahl Schäden in Teilkasko")
    description_of_claims_in_partial_coverage_insurance = fields.Text(string="Beschreibung der Schäden in Teilkasko")

    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('reviewed', 'Reviewed'),
    ], default='draft', tracking=True)

    # Company field for multi-company access control
    company_id = fields.Many2one('res.company', string='Company',
                                default=lambda self: self.env.company,
                                required=True,
                                help="Company that this request belongs to")

    # SQL constraints
    _sql_constraints = [
        ('company_id_required', 'CHECK (company_id IS NOT NULL)',
         'Company is required for all insurance requests.'),
    ]

    # Override create method to ensure CRM lead creation
    @api.model
    def create(self, vals):
        # Generate sequence number if needed
        if vals.get('name', 'New') == 'New':
            vals['name'] = self.env['ir.sequence'].next_by_code('premium.car.request') or 'New'
            _logger.info(f"Generated reference number: {vals['name']}")

        # If no lead_id is provided, create a new lead
        if not vals.get('lead_id'):
            # Use the opportunity mapper to map form fields to opportunity fields
            try:
                lead_vals = self.env['opportunity.mapper'].map_form_to_opportunity(vals, 'premium_car')

                # Ensure basic lead information is set
                if not lead_vals.get('name'):
                    lead_vals['name'] = vals.get('name', 'Premium Car Request')
                if not lead_vals.get('type'):
                    lead_vals['type'] = 'lead'  # Create as lead, not opportunity
                if not lead_vals.get('phone') and vals.get('contact_phone'):
                    lead_vals['phone'] = vals.get('contact_phone')
                if not lead_vals.get('email_from') and vals.get('contact_email'):
                    lead_vals['email_from'] = vals.get('contact_email')
                if not lead_vals.get('contact_name'):
                    lead_vals['contact_name'] = f"{vals.get('first_name', '')} {vals.get('last_name', '')}".strip()

                # Create the lead/opportunity
                lead = self.env['crm.lead'].sudo().create(lead_vals)
                vals['lead_id'] = lead.id


            except Exception as e:
                _logger.error(f"Error creating CRM lead from premium car request: {e}")
                # Continue with form creation even if lead creation fails

        # If we have a lead_id but no company_id, try to get it from the existing lead
        if vals.get('lead_id') and not vals.get('company_id'):
            try:
                lead = self.env['crm.lead'].sudo().browse(vals['lead_id'])
                if lead.exists() and lead.policy_provider_id:
                    vals['company_id'] = lead.policy_provider_id.id
                    _logger.info(f"Set company_id to {lead.policy_provider_id.name} from existing CRM lead")
            except Exception as e:
                _logger.error(f"Error getting company from existing CRM lead: {e}")

        return super(PremiumCarRequest, self).create(vals)

    def action_submit(self):
        for rec in self:
            rec.state = 'submitted'



    def _get_salutation_display(self):
        """Get display value for salutation"""
        if not hasattr(self, 'salutation') or not self.salutation:
            return ''
        salutation_dict = {
            'mr': 'Herr',
            'mrs': 'Frau',
            'diverse': 'Divers',
            'company': 'Firma',
            'other': 'Sonstige'
        }
        return salutation_dict.get(self.salutation, '')

    def _get_customer_authority_display(self):
        """Get display value for customer authority"""
        if not hasattr(self, 'has_customer_authority') or not self.has_customer_authority:
            return ''
        authority_dict = {
            'yes': 'Ja',
            'no': 'Nein'
        }
        return authority_dict.get(self.has_customer_authority, '')

    def _get_coverage_type_display(self):
        """Get display value for coverage type"""
        if not hasattr(self, 'coverage_type') or not self.coverage_type:
            return ''
        coverage_dict = {
            'liability': 'Haftpflicht',
            'partial': 'Teilkasko',
            'full': 'Vollkasko'
        }
        return coverage_dict.get(self.coverage_type, '')

    def _get_previous_insurance_display(self):
        """Get display value for previous insurance"""
        if hasattr(self, 'existing_insurance') and self.existing_insurance:
            return 'Ja'
        elif hasattr(self, 'has_previous_insurance'):
            return 'Ja' if self.has_previous_insurance == 'yes' else 'Nein'
        return ''
